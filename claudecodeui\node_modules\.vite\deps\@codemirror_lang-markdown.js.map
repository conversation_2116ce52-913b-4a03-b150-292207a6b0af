{"version": 3, "sources": ["../../@lezer/markdown/dist/index.js", "../../@codemirror/lang-markdown/dist/index.js"], "sourcesContent": ["import { NodeType, NodeProp, NodeSet, Tree, Parser, parseMixed } from '@lezer/common';\nimport { styleTags, tags, Tag } from '@lezer/highlight';\n\nclass CompositeBlock {\n    static create(type, value, from, parentHash, end) {\n        let hash = (parentHash + (parentHash << 8) + type + (value << 4)) | 0;\n        return new CompositeBlock(type, value, from, hash, end, [], []);\n    }\n    constructor(type, \n    // Used for indentation in list items, markup character in lists\n    value, from, hash, end, children, positions) {\n        this.type = type;\n        this.value = value;\n        this.from = from;\n        this.hash = hash;\n        this.end = end;\n        this.children = children;\n        this.positions = positions;\n        this.hashProp = [[NodeProp.contextHash, hash]];\n    }\n    addChild(child, pos) {\n        if (child.prop(NodeProp.contextHash) != this.hash)\n            child = new Tree(child.type, child.children, child.positions, child.length, this.hashProp);\n        this.children.push(child);\n        this.positions.push(pos);\n    }\n    toTree(nodeSet, end = this.end) {\n        let last = this.children.length - 1;\n        if (last >= 0)\n            end = Math.max(end, this.positions[last] + this.children[last].length + this.from);\n        return new Tree(nodeSet.types[this.type], this.children, this.positions, end - this.from).balance({\n            makeTree: (children, positions, length) => new Tree(NodeType.none, children, positions, length, this.hashProp)\n        });\n    }\n}\nvar Type;\n(function (Type) {\n    Type[Type[\"Document\"] = 1] = \"Document\";\n    Type[Type[\"CodeBlock\"] = 2] = \"CodeBlock\";\n    Type[Type[\"FencedCode\"] = 3] = \"FencedCode\";\n    Type[Type[\"Blockquote\"] = 4] = \"Blockquote\";\n    Type[Type[\"HorizontalRule\"] = 5] = \"HorizontalRule\";\n    Type[Type[\"BulletList\"] = 6] = \"BulletList\";\n    Type[Type[\"OrderedList\"] = 7] = \"OrderedList\";\n    Type[Type[\"ListItem\"] = 8] = \"ListItem\";\n    Type[Type[\"ATXHeading1\"] = 9] = \"ATXHeading1\";\n    Type[Type[\"ATXHeading2\"] = 10] = \"ATXHeading2\";\n    Type[Type[\"ATXHeading3\"] = 11] = \"ATXHeading3\";\n    Type[Type[\"ATXHeading4\"] = 12] = \"ATXHeading4\";\n    Type[Type[\"ATXHeading5\"] = 13] = \"ATXHeading5\";\n    Type[Type[\"ATXHeading6\"] = 14] = \"ATXHeading6\";\n    Type[Type[\"SetextHeading1\"] = 15] = \"SetextHeading1\";\n    Type[Type[\"SetextHeading2\"] = 16] = \"SetextHeading2\";\n    Type[Type[\"HTMLBlock\"] = 17] = \"HTMLBlock\";\n    Type[Type[\"LinkReference\"] = 18] = \"LinkReference\";\n    Type[Type[\"Paragraph\"] = 19] = \"Paragraph\";\n    Type[Type[\"CommentBlock\"] = 20] = \"CommentBlock\";\n    Type[Type[\"ProcessingInstructionBlock\"] = 21] = \"ProcessingInstructionBlock\";\n    // Inline\n    Type[Type[\"Escape\"] = 22] = \"Escape\";\n    Type[Type[\"Entity\"] = 23] = \"Entity\";\n    Type[Type[\"HardBreak\"] = 24] = \"HardBreak\";\n    Type[Type[\"Emphasis\"] = 25] = \"Emphasis\";\n    Type[Type[\"StrongEmphasis\"] = 26] = \"StrongEmphasis\";\n    Type[Type[\"Link\"] = 27] = \"Link\";\n    Type[Type[\"Image\"] = 28] = \"Image\";\n    Type[Type[\"InlineCode\"] = 29] = \"InlineCode\";\n    Type[Type[\"HTMLTag\"] = 30] = \"HTMLTag\";\n    Type[Type[\"Comment\"] = 31] = \"Comment\";\n    Type[Type[\"ProcessingInstruction\"] = 32] = \"ProcessingInstruction\";\n    Type[Type[\"Autolink\"] = 33] = \"Autolink\";\n    // Smaller tokens\n    Type[Type[\"HeaderMark\"] = 34] = \"HeaderMark\";\n    Type[Type[\"QuoteMark\"] = 35] = \"QuoteMark\";\n    Type[Type[\"ListMark\"] = 36] = \"ListMark\";\n    Type[Type[\"LinkMark\"] = 37] = \"LinkMark\";\n    Type[Type[\"EmphasisMark\"] = 38] = \"EmphasisMark\";\n    Type[Type[\"CodeMark\"] = 39] = \"CodeMark\";\n    Type[Type[\"CodeText\"] = 40] = \"CodeText\";\n    Type[Type[\"CodeInfo\"] = 41] = \"CodeInfo\";\n    Type[Type[\"LinkTitle\"] = 42] = \"LinkTitle\";\n    Type[Type[\"LinkLabel\"] = 43] = \"LinkLabel\";\n    Type[Type[\"URL\"] = 44] = \"URL\";\n})(Type || (Type = {}));\n/**\nData structure used to accumulate a block's content during [leaf\nblock parsing](#BlockParser.leaf).\n*/\nclass LeafBlock {\n    /**\n    @internal\n    */\n    constructor(\n    /**\n    The start position of the block.\n    */\n    start, \n    /**\n    The block's text content.\n    */\n    content) {\n        this.start = start;\n        this.content = content;\n        /**\n        @internal\n        */\n        this.marks = [];\n        /**\n        The block parsers active for this block.\n        */\n        this.parsers = [];\n    }\n}\n/**\nData structure used during block-level per-line parsing.\n*/\nclass Line {\n    constructor() {\n        /**\n        The line's full text.\n        */\n        this.text = \"\";\n        /**\n        The base indent provided by the composite contexts (that have\n        been handled so far).\n        */\n        this.baseIndent = 0;\n        /**\n        The string position corresponding to the base indent.\n        */\n        this.basePos = 0;\n        /**\n        The number of contexts handled @internal\n        */\n        this.depth = 0;\n        /**\n        Any markers (i.e. block quote markers) parsed for the contexts. @internal\n        */\n        this.markers = [];\n        /**\n        The position of the next non-whitespace character beyond any\n        list, blockquote, or other composite block markers.\n        */\n        this.pos = 0;\n        /**\n        The column of the next non-whitespace character.\n        */\n        this.indent = 0;\n        /**\n        The character code of the character after `pos`.\n        */\n        this.next = -1;\n    }\n    /**\n    @internal\n    */\n    forward() {\n        if (this.basePos > this.pos)\n            this.forwardInner();\n    }\n    /**\n    @internal\n    */\n    forwardInner() {\n        let newPos = this.skipSpace(this.basePos);\n        this.indent = this.countIndent(newPos, this.pos, this.indent);\n        this.pos = newPos;\n        this.next = newPos == this.text.length ? -1 : this.text.charCodeAt(newPos);\n    }\n    /**\n    Skip whitespace after the given position, return the position of\n    the next non-space character or the end of the line if there's\n    only space after `from`.\n    */\n    skipSpace(from) { return skipSpace(this.text, from); }\n    /**\n    @internal\n    */\n    reset(text) {\n        this.text = text;\n        this.baseIndent = this.basePos = this.pos = this.indent = 0;\n        this.forwardInner();\n        this.depth = 1;\n        while (this.markers.length)\n            this.markers.pop();\n    }\n    /**\n    Move the line's base position forward to the given position.\n    This should only be called by composite [block\n    parsers](#BlockParser.parse) or [markup skipping\n    functions](#NodeSpec.composite).\n    */\n    moveBase(to) {\n        this.basePos = to;\n        this.baseIndent = this.countIndent(to, this.pos, this.indent);\n    }\n    /**\n    Move the line's base position forward to the given _column_.\n    */\n    moveBaseColumn(indent) {\n        this.baseIndent = indent;\n        this.basePos = this.findColumn(indent);\n    }\n    /**\n    Store a composite-block-level marker. Should be called from\n    [markup skipping functions](#NodeSpec.composite) when they\n    consume any non-whitespace characters.\n    */\n    addMarker(elt) {\n        this.markers.push(elt);\n    }\n    /**\n    Find the column position at `to`, optionally starting at a given\n    position and column.\n    */\n    countIndent(to, from = 0, indent = 0) {\n        for (let i = from; i < to; i++)\n            indent += this.text.charCodeAt(i) == 9 ? 4 - indent % 4 : 1;\n        return indent;\n    }\n    /**\n    Find the position corresponding to the given column.\n    */\n    findColumn(goal) {\n        let i = 0;\n        for (let indent = 0; i < this.text.length && indent < goal; i++)\n            indent += this.text.charCodeAt(i) == 9 ? 4 - indent % 4 : 1;\n        return i;\n    }\n    /**\n    @internal\n    */\n    scrub() {\n        if (!this.baseIndent)\n            return this.text;\n        let result = \"\";\n        for (let i = 0; i < this.basePos; i++)\n            result += \" \";\n        return result + this.text.slice(this.basePos);\n    }\n}\nfunction skipForList(bl, cx, line) {\n    if (line.pos == line.text.length ||\n        (bl != cx.block && line.indent >= cx.stack[line.depth + 1].value + line.baseIndent))\n        return true;\n    if (line.indent >= line.baseIndent + 4)\n        return false;\n    let size = (bl.type == Type.OrderedList ? isOrderedList : isBulletList)(line, cx, false);\n    return size > 0 &&\n        (bl.type != Type.BulletList || isHorizontalRule(line, cx, false) < 0) &&\n        line.text.charCodeAt(line.pos + size - 1) == bl.value;\n}\nconst DefaultSkipMarkup = {\n    [Type.Blockquote](bl, cx, line) {\n        if (line.next != 62 /* '>' */)\n            return false;\n        line.markers.push(elt(Type.QuoteMark, cx.lineStart + line.pos, cx.lineStart + line.pos + 1));\n        line.moveBase(line.pos + (space(line.text.charCodeAt(line.pos + 1)) ? 2 : 1));\n        bl.end = cx.lineStart + line.text.length;\n        return true;\n    },\n    [Type.ListItem](bl, _cx, line) {\n        if (line.indent < line.baseIndent + bl.value && line.next > -1)\n            return false;\n        line.moveBaseColumn(line.baseIndent + bl.value);\n        return true;\n    },\n    [Type.OrderedList]: skipForList,\n    [Type.BulletList]: skipForList,\n    [Type.Document]() { return true; }\n};\nfunction space(ch) { return ch == 32 || ch == 9 || ch == 10 || ch == 13; }\nfunction skipSpace(line, i = 0) {\n    while (i < line.length && space(line.charCodeAt(i)))\n        i++;\n    return i;\n}\nfunction skipSpaceBack(line, i, to) {\n    while (i > to && space(line.charCodeAt(i - 1)))\n        i--;\n    return i;\n}\nfunction isFencedCode(line) {\n    if (line.next != 96 && line.next != 126 /* '`~' */)\n        return -1;\n    let pos = line.pos + 1;\n    while (pos < line.text.length && line.text.charCodeAt(pos) == line.next)\n        pos++;\n    if (pos < line.pos + 3)\n        return -1;\n    if (line.next == 96)\n        for (let i = pos; i < line.text.length; i++)\n            if (line.text.charCodeAt(i) == 96)\n                return -1;\n    return pos;\n}\nfunction isBlockquote(line) {\n    return line.next != 62 /* '>' */ ? -1 : line.text.charCodeAt(line.pos + 1) == 32 ? 2 : 1;\n}\nfunction isHorizontalRule(line, cx, breaking) {\n    if (line.next != 42 && line.next != 45 && line.next != 95 /* '_-*' */)\n        return -1;\n    let count = 1;\n    for (let pos = line.pos + 1; pos < line.text.length; pos++) {\n        let ch = line.text.charCodeAt(pos);\n        if (ch == line.next)\n            count++;\n        else if (!space(ch))\n            return -1;\n    }\n    // Setext headers take precedence\n    if (breaking && line.next == 45 && isSetextUnderline(line) > -1 && line.depth == cx.stack.length &&\n        cx.parser.leafBlockParsers.indexOf(DefaultLeafBlocks.SetextHeading) > -1)\n        return -1;\n    return count < 3 ? -1 : 1;\n}\nfunction inList(cx, type) {\n    for (let i = cx.stack.length - 1; i >= 0; i--)\n        if (cx.stack[i].type == type)\n            return true;\n    return false;\n}\nfunction isBulletList(line, cx, breaking) {\n    return (line.next == 45 || line.next == 43 || line.next == 42 /* '-+*' */) &&\n        (line.pos == line.text.length - 1 || space(line.text.charCodeAt(line.pos + 1))) &&\n        (!breaking || inList(cx, Type.BulletList) || line.skipSpace(line.pos + 2) < line.text.length) ? 1 : -1;\n}\nfunction isOrderedList(line, cx, breaking) {\n    let pos = line.pos, next = line.next;\n    for (;;) {\n        if (next >= 48 && next <= 57 /* '0-9' */)\n            pos++;\n        else\n            break;\n        if (pos == line.text.length)\n            return -1;\n        next = line.text.charCodeAt(pos);\n    }\n    if (pos == line.pos || pos > line.pos + 9 ||\n        (next != 46 && next != 41 /* '.)' */) ||\n        (pos < line.text.length - 1 && !space(line.text.charCodeAt(pos + 1))) ||\n        breaking && !inList(cx, Type.OrderedList) &&\n            (line.skipSpace(pos + 1) == line.text.length || pos > line.pos + 1 || line.next != 49 /* '1' */))\n        return -1;\n    return pos + 1 - line.pos;\n}\nfunction isAtxHeading(line) {\n    if (line.next != 35 /* '#' */)\n        return -1;\n    let pos = line.pos + 1;\n    while (pos < line.text.length && line.text.charCodeAt(pos) == 35)\n        pos++;\n    if (pos < line.text.length && line.text.charCodeAt(pos) != 32)\n        return -1;\n    let size = pos - line.pos;\n    return size > 6 ? -1 : size;\n}\nfunction isSetextUnderline(line) {\n    if (line.next != 45 && line.next != 61 /* '-=' */ || line.indent >= line.baseIndent + 4)\n        return -1;\n    let pos = line.pos + 1;\n    while (pos < line.text.length && line.text.charCodeAt(pos) == line.next)\n        pos++;\n    let end = pos;\n    while (pos < line.text.length && space(line.text.charCodeAt(pos)))\n        pos++;\n    return pos == line.text.length ? end : -1;\n}\nconst EmptyLine = /^[ \\t]*$/, CommentEnd = /-->/, ProcessingEnd = /\\?>/;\nconst HTMLBlockStyle = [\n    [/^<(?:script|pre|style)(?:\\s|>|$)/i, /<\\/(?:script|pre|style)>/i],\n    [/^\\s*<!--/, CommentEnd],\n    [/^\\s*<\\?/, ProcessingEnd],\n    [/^\\s*<![A-Z]/, />/],\n    [/^\\s*<!\\[CDATA\\[/, /\\]\\]>/],\n    [/^\\s*<\\/?(?:address|article|aside|base|basefont|blockquote|body|caption|center|col|colgroup|dd|details|dialog|dir|div|dl|dt|fieldset|figcaption|figure|footer|form|frame|frameset|h1|h2|h3|h4|h5|h6|head|header|hr|html|iframe|legend|li|link|main|menu|menuitem|nav|noframes|ol|optgroup|option|p|param|section|source|summary|table|tbody|td|tfoot|th|thead|title|tr|track|ul)(?:\\s|\\/?>|$)/i, EmptyLine],\n    [/^\\s*(?:<\\/[a-z][\\w-]*\\s*>|<[a-z][\\w-]*(\\s+[a-z:_][\\w-.]*(?:\\s*=\\s*(?:[^\\s\"'=<>`]+|'[^']*'|\"[^\"]*\"))?)*\\s*>)\\s*$/i, EmptyLine]\n];\nfunction isHTMLBlock(line, _cx, breaking) {\n    if (line.next != 60 /* '<' */)\n        return -1;\n    let rest = line.text.slice(line.pos);\n    for (let i = 0, e = HTMLBlockStyle.length - (breaking ? 1 : 0); i < e; i++)\n        if (HTMLBlockStyle[i][0].test(rest))\n            return i;\n    return -1;\n}\nfunction getListIndent(line, pos) {\n    let indentAfter = line.countIndent(pos, line.pos, line.indent);\n    let indented = line.countIndent(line.skipSpace(pos), pos, indentAfter);\n    return indented >= indentAfter + 5 ? indentAfter + 1 : indented;\n}\nfunction addCodeText(marks, from, to) {\n    let last = marks.length - 1;\n    if (last >= 0 && marks[last].to == from && marks[last].type == Type.CodeText)\n        marks[last].to = to;\n    else\n        marks.push(elt(Type.CodeText, from, to));\n}\n// Rules for parsing blocks. A return value of false means the rule\n// doesn't apply here, true means it does. When true is returned and\n// `p.line` has been updated, the rule is assumed to have consumed a\n// leaf block. Otherwise, it is assumed to have opened a context.\nconst DefaultBlockParsers = {\n    LinkReference: undefined,\n    IndentedCode(cx, line) {\n        let base = line.baseIndent + 4;\n        if (line.indent < base)\n            return false;\n        let start = line.findColumn(base);\n        let from = cx.lineStart + start, to = cx.lineStart + line.text.length;\n        let marks = [], pendingMarks = [];\n        addCodeText(marks, from, to);\n        while (cx.nextLine() && line.depth >= cx.stack.length) {\n            if (line.pos == line.text.length) { // Empty\n                addCodeText(pendingMarks, cx.lineStart - 1, cx.lineStart);\n                for (let m of line.markers)\n                    pendingMarks.push(m);\n            }\n            else if (line.indent < base) {\n                break;\n            }\n            else {\n                if (pendingMarks.length) {\n                    for (let m of pendingMarks) {\n                        if (m.type == Type.CodeText)\n                            addCodeText(marks, m.from, m.to);\n                        else\n                            marks.push(m);\n                    }\n                    pendingMarks = [];\n                }\n                addCodeText(marks, cx.lineStart - 1, cx.lineStart);\n                for (let m of line.markers)\n                    marks.push(m);\n                to = cx.lineStart + line.text.length;\n                let codeStart = cx.lineStart + line.findColumn(line.baseIndent + 4);\n                if (codeStart < to)\n                    addCodeText(marks, codeStart, to);\n            }\n        }\n        if (pendingMarks.length) {\n            pendingMarks = pendingMarks.filter(m => m.type != Type.CodeText);\n            if (pendingMarks.length)\n                line.markers = pendingMarks.concat(line.markers);\n        }\n        cx.addNode(cx.buffer.writeElements(marks, -from).finish(Type.CodeBlock, to - from), from);\n        return true;\n    },\n    FencedCode(cx, line) {\n        let fenceEnd = isFencedCode(line);\n        if (fenceEnd < 0)\n            return false;\n        let from = cx.lineStart + line.pos, ch = line.next, len = fenceEnd - line.pos;\n        let infoFrom = line.skipSpace(fenceEnd), infoTo = skipSpaceBack(line.text, line.text.length, infoFrom);\n        let marks = [elt(Type.CodeMark, from, from + len)];\n        if (infoFrom < infoTo)\n            marks.push(elt(Type.CodeInfo, cx.lineStart + infoFrom, cx.lineStart + infoTo));\n        for (let first = true; cx.nextLine() && line.depth >= cx.stack.length; first = false) {\n            let i = line.pos;\n            if (line.indent - line.baseIndent < 4)\n                while (i < line.text.length && line.text.charCodeAt(i) == ch)\n                    i++;\n            if (i - line.pos >= len && line.skipSpace(i) == line.text.length) {\n                for (let m of line.markers)\n                    marks.push(m);\n                marks.push(elt(Type.CodeMark, cx.lineStart + line.pos, cx.lineStart + i));\n                cx.nextLine();\n                break;\n            }\n            else {\n                if (!first)\n                    addCodeText(marks, cx.lineStart - 1, cx.lineStart);\n                for (let m of line.markers)\n                    marks.push(m);\n                let textStart = cx.lineStart + line.basePos, textEnd = cx.lineStart + line.text.length;\n                if (textStart < textEnd)\n                    addCodeText(marks, textStart, textEnd);\n            }\n        }\n        cx.addNode(cx.buffer.writeElements(marks, -from)\n            .finish(Type.FencedCode, cx.prevLineEnd() - from), from);\n        return true;\n    },\n    Blockquote(cx, line) {\n        let size = isBlockquote(line);\n        if (size < 0)\n            return false;\n        cx.startContext(Type.Blockquote, line.pos);\n        cx.addNode(Type.QuoteMark, cx.lineStart + line.pos, cx.lineStart + line.pos + 1);\n        line.moveBase(line.pos + size);\n        return null;\n    },\n    HorizontalRule(cx, line) {\n        if (isHorizontalRule(line, cx, false) < 0)\n            return false;\n        let from = cx.lineStart + line.pos;\n        cx.nextLine();\n        cx.addNode(Type.HorizontalRule, from);\n        return true;\n    },\n    BulletList(cx, line) {\n        let size = isBulletList(line, cx, false);\n        if (size < 0)\n            return false;\n        if (cx.block.type != Type.BulletList)\n            cx.startContext(Type.BulletList, line.basePos, line.next);\n        let newBase = getListIndent(line, line.pos + 1);\n        cx.startContext(Type.ListItem, line.basePos, newBase - line.baseIndent);\n        cx.addNode(Type.ListMark, cx.lineStart + line.pos, cx.lineStart + line.pos + size);\n        line.moveBaseColumn(newBase);\n        return null;\n    },\n    OrderedList(cx, line) {\n        let size = isOrderedList(line, cx, false);\n        if (size < 0)\n            return false;\n        if (cx.block.type != Type.OrderedList)\n            cx.startContext(Type.OrderedList, line.basePos, line.text.charCodeAt(line.pos + size - 1));\n        let newBase = getListIndent(line, line.pos + size);\n        cx.startContext(Type.ListItem, line.basePos, newBase - line.baseIndent);\n        cx.addNode(Type.ListMark, cx.lineStart + line.pos, cx.lineStart + line.pos + size);\n        line.moveBaseColumn(newBase);\n        return null;\n    },\n    ATXHeading(cx, line) {\n        let size = isAtxHeading(line);\n        if (size < 0)\n            return false;\n        let off = line.pos, from = cx.lineStart + off;\n        let endOfSpace = skipSpaceBack(line.text, line.text.length, off), after = endOfSpace;\n        while (after > off && line.text.charCodeAt(after - 1) == line.next)\n            after--;\n        if (after == endOfSpace || after == off || !space(line.text.charCodeAt(after - 1)))\n            after = line.text.length;\n        let buf = cx.buffer\n            .write(Type.HeaderMark, 0, size)\n            .writeElements(cx.parser.parseInline(line.text.slice(off + size + 1, after), from + size + 1), -from);\n        if (after < line.text.length)\n            buf.write(Type.HeaderMark, after - off, endOfSpace - off);\n        let node = buf.finish(Type.ATXHeading1 - 1 + size, line.text.length - off);\n        cx.nextLine();\n        cx.addNode(node, from);\n        return true;\n    },\n    HTMLBlock(cx, line) {\n        let type = isHTMLBlock(line, cx, false);\n        if (type < 0)\n            return false;\n        let from = cx.lineStart + line.pos, end = HTMLBlockStyle[type][1];\n        let marks = [], trailing = end != EmptyLine;\n        while (!end.test(line.text) && cx.nextLine()) {\n            if (line.depth < cx.stack.length) {\n                trailing = false;\n                break;\n            }\n            for (let m of line.markers)\n                marks.push(m);\n        }\n        if (trailing)\n            cx.nextLine();\n        let nodeType = end == CommentEnd ? Type.CommentBlock : end == ProcessingEnd ? Type.ProcessingInstructionBlock : Type.HTMLBlock;\n        let to = cx.prevLineEnd();\n        cx.addNode(cx.buffer.writeElements(marks, -from).finish(nodeType, to - from), from);\n        return true;\n    },\n    SetextHeading: undefined // Specifies relative precedence for block-continue function\n};\n// This implements a state machine that incrementally parses link references. At each\n// next line, it looks ahead to see if the line continues the reference or not. If it\n// doesn't and a valid link is available ending before that line, it finishes that.\n// Similarly, on `finish` (when the leaf is terminated by external circumstances), it\n// creates a link reference if there's a valid reference up to the current point.\nclass LinkReferenceParser {\n    constructor(leaf) {\n        this.stage = 0 /* RefStage.Start */;\n        this.elts = [];\n        this.pos = 0;\n        this.start = leaf.start;\n        this.advance(leaf.content);\n    }\n    nextLine(cx, line, leaf) {\n        if (this.stage == -1 /* RefStage.Failed */)\n            return false;\n        let content = leaf.content + \"\\n\" + line.scrub();\n        let finish = this.advance(content);\n        if (finish > -1 && finish < content.length)\n            return this.complete(cx, leaf, finish);\n        return false;\n    }\n    finish(cx, leaf) {\n        if ((this.stage == 2 /* RefStage.Link */ || this.stage == 3 /* RefStage.Title */) && skipSpace(leaf.content, this.pos) == leaf.content.length)\n            return this.complete(cx, leaf, leaf.content.length);\n        return false;\n    }\n    complete(cx, leaf, len) {\n        cx.addLeafElement(leaf, elt(Type.LinkReference, this.start, this.start + len, this.elts));\n        return true;\n    }\n    nextStage(elt) {\n        if (elt) {\n            this.pos = elt.to - this.start;\n            this.elts.push(elt);\n            this.stage++;\n            return true;\n        }\n        if (elt === false)\n            this.stage = -1 /* RefStage.Failed */;\n        return false;\n    }\n    advance(content) {\n        for (;;) {\n            if (this.stage == -1 /* RefStage.Failed */) {\n                return -1;\n            }\n            else if (this.stage == 0 /* RefStage.Start */) {\n                if (!this.nextStage(parseLinkLabel(content, this.pos, this.start, true)))\n                    return -1;\n                if (content.charCodeAt(this.pos) != 58 /* ':' */)\n                    return this.stage = -1 /* RefStage.Failed */;\n                this.elts.push(elt(Type.LinkMark, this.pos + this.start, this.pos + this.start + 1));\n                this.pos++;\n            }\n            else if (this.stage == 1 /* RefStage.Label */) {\n                if (!this.nextStage(parseURL(content, skipSpace(content, this.pos), this.start)))\n                    return -1;\n            }\n            else if (this.stage == 2 /* RefStage.Link */) {\n                let skip = skipSpace(content, this.pos), end = 0;\n                if (skip > this.pos) {\n                    let title = parseLinkTitle(content, skip, this.start);\n                    if (title) {\n                        let titleEnd = lineEnd(content, title.to - this.start);\n                        if (titleEnd > 0) {\n                            this.nextStage(title);\n                            end = titleEnd;\n                        }\n                    }\n                }\n                if (!end)\n                    end = lineEnd(content, this.pos);\n                return end > 0 && end < content.length ? end : -1;\n            }\n            else { // RefStage.Title\n                return lineEnd(content, this.pos);\n            }\n        }\n    }\n}\nfunction lineEnd(text, pos) {\n    for (; pos < text.length; pos++) {\n        let next = text.charCodeAt(pos);\n        if (next == 10)\n            break;\n        if (!space(next))\n            return -1;\n    }\n    return pos;\n}\nclass SetextHeadingParser {\n    nextLine(cx, line, leaf) {\n        let underline = line.depth < cx.stack.length ? -1 : isSetextUnderline(line);\n        let next = line.next;\n        if (underline < 0)\n            return false;\n        let underlineMark = elt(Type.HeaderMark, cx.lineStart + line.pos, cx.lineStart + underline);\n        cx.nextLine();\n        cx.addLeafElement(leaf, elt(next == 61 ? Type.SetextHeading1 : Type.SetextHeading2, leaf.start, cx.prevLineEnd(), [\n            ...cx.parser.parseInline(leaf.content, leaf.start),\n            underlineMark\n        ]));\n        return true;\n    }\n    finish() {\n        return false;\n    }\n}\nconst DefaultLeafBlocks = {\n    LinkReference(_, leaf) { return leaf.content.charCodeAt(0) == 91 /* '[' */ ? new LinkReferenceParser(leaf) : null; },\n    SetextHeading() { return new SetextHeadingParser; }\n};\nconst DefaultEndLeaf = [\n    (_, line) => isAtxHeading(line) >= 0,\n    (_, line) => isFencedCode(line) >= 0,\n    (_, line) => isBlockquote(line) >= 0,\n    (p, line) => isBulletList(line, p, true) >= 0,\n    (p, line) => isOrderedList(line, p, true) >= 0,\n    (p, line) => isHorizontalRule(line, p, true) >= 0,\n    (p, line) => isHTMLBlock(line, p, true) >= 0\n];\nconst scanLineResult = { text: \"\", end: 0 };\n/**\nBlock-level parsing functions get access to this context object.\n*/\nclass BlockContext {\n    /**\n    @internal\n    */\n    constructor(\n    /**\n    The parser configuration used.\n    */\n    parser, \n    /**\n    @internal\n    */\n    input, fragments, \n    /**\n    @internal\n    */\n    ranges) {\n        this.parser = parser;\n        this.input = input;\n        this.ranges = ranges;\n        this.line = new Line();\n        this.atEnd = false;\n        /**\n        For reused nodes on gaps, we can't directly put the original\n        node into the tree, since that may be bigger than its parent.\n        When this happens, we create a dummy tree that is replaced by\n        the proper node in `injectGaps` @internal\n        */\n        this.reusePlaceholders = new Map;\n        this.stoppedAt = null;\n        /**\n        The range index that absoluteLineStart points into @internal\n        */\n        this.rangeI = 0;\n        this.to = ranges[ranges.length - 1].to;\n        this.lineStart = this.absoluteLineStart = this.absoluteLineEnd = ranges[0].from;\n        this.block = CompositeBlock.create(Type.Document, 0, this.lineStart, 0, 0);\n        this.stack = [this.block];\n        this.fragments = fragments.length ? new FragmentCursor(fragments, input) : null;\n        this.readLine();\n    }\n    get parsedPos() {\n        return this.absoluteLineStart;\n    }\n    advance() {\n        if (this.stoppedAt != null && this.absoluteLineStart > this.stoppedAt)\n            return this.finish();\n        let { line } = this;\n        for (;;) {\n            for (let markI = 0;;) {\n                let next = line.depth < this.stack.length ? this.stack[this.stack.length - 1] : null;\n                while (markI < line.markers.length && (!next || line.markers[markI].from < next.end)) {\n                    let mark = line.markers[markI++];\n                    this.addNode(mark.type, mark.from, mark.to);\n                }\n                if (!next)\n                    break;\n                this.finishContext();\n            }\n            if (line.pos < line.text.length)\n                break;\n            // Empty line\n            if (!this.nextLine())\n                return this.finish();\n        }\n        if (this.fragments && this.reuseFragment(line.basePos))\n            return null;\n        start: for (;;) {\n            for (let type of this.parser.blockParsers)\n                if (type) {\n                    let result = type(this, line);\n                    if (result != false) {\n                        if (result == true)\n                            return null;\n                        line.forward();\n                        continue start;\n                    }\n                }\n            break;\n        }\n        let leaf = new LeafBlock(this.lineStart + line.pos, line.text.slice(line.pos));\n        for (let parse of this.parser.leafBlockParsers)\n            if (parse) {\n                let parser = parse(this, leaf);\n                if (parser)\n                    leaf.parsers.push(parser);\n            }\n        lines: while (this.nextLine()) {\n            if (line.pos == line.text.length)\n                break;\n            if (line.indent < line.baseIndent + 4) {\n                for (let stop of this.parser.endLeafBlock)\n                    if (stop(this, line, leaf))\n                        break lines;\n            }\n            for (let parser of leaf.parsers)\n                if (parser.nextLine(this, line, leaf))\n                    return null;\n            leaf.content += \"\\n\" + line.scrub();\n            for (let m of line.markers)\n                leaf.marks.push(m);\n        }\n        this.finishLeaf(leaf);\n        return null;\n    }\n    stopAt(pos) {\n        if (this.stoppedAt != null && this.stoppedAt < pos)\n            throw new RangeError(\"Can't move stoppedAt forward\");\n        this.stoppedAt = pos;\n    }\n    reuseFragment(start) {\n        if (!this.fragments.moveTo(this.absoluteLineStart + start, this.absoluteLineStart) ||\n            !this.fragments.matches(this.block.hash))\n            return false;\n        let taken = this.fragments.takeNodes(this);\n        if (!taken)\n            return false;\n        this.absoluteLineStart += taken;\n        this.lineStart = toRelative(this.absoluteLineStart, this.ranges);\n        this.moveRangeI();\n        if (this.absoluteLineStart < this.to) {\n            this.lineStart++;\n            this.absoluteLineStart++;\n            this.readLine();\n        }\n        else {\n            this.atEnd = true;\n            this.readLine();\n        }\n        return true;\n    }\n    /**\n    The number of parent blocks surrounding the current block.\n    */\n    get depth() {\n        return this.stack.length;\n    }\n    /**\n    Get the type of the parent block at the given depth. When no\n    depth is passed, return the type of the innermost parent.\n    */\n    parentType(depth = this.depth - 1) {\n        return this.parser.nodeSet.types[this.stack[depth].type];\n    }\n    /**\n    Move to the next input line. This should only be called by\n    (non-composite) [block parsers](#BlockParser.parse) that consume\n    the line directly, or leaf block parser\n    [`nextLine`](#LeafBlockParser.nextLine) methods when they\n    consume the current line (and return true).\n    */\n    nextLine() {\n        this.lineStart += this.line.text.length;\n        if (this.absoluteLineEnd >= this.to) {\n            this.absoluteLineStart = this.absoluteLineEnd;\n            this.atEnd = true;\n            this.readLine();\n            return false;\n        }\n        else {\n            this.lineStart++;\n            this.absoluteLineStart = this.absoluteLineEnd + 1;\n            this.moveRangeI();\n            this.readLine();\n            return true;\n        }\n    }\n    /**\n    Retrieve the text of the line after the current one, without\n    actually moving the context's current line forward.\n    */\n    peekLine() {\n        return this.scanLine(this.absoluteLineEnd + 1).text;\n    }\n    moveRangeI() {\n        while (this.rangeI < this.ranges.length - 1 && this.absoluteLineStart >= this.ranges[this.rangeI].to) {\n            this.rangeI++;\n            this.absoluteLineStart = Math.max(this.absoluteLineStart, this.ranges[this.rangeI].from);\n        }\n    }\n    /**\n    @internal\n    Collect the text for the next line.\n    */\n    scanLine(start) {\n        let r = scanLineResult;\n        r.end = start;\n        if (start >= this.to) {\n            r.text = \"\";\n        }\n        else {\n            r.text = this.lineChunkAt(start);\n            r.end += r.text.length;\n            if (this.ranges.length > 1) {\n                let textOffset = this.absoluteLineStart, rangeI = this.rangeI;\n                while (this.ranges[rangeI].to < r.end) {\n                    rangeI++;\n                    let nextFrom = this.ranges[rangeI].from;\n                    let after = this.lineChunkAt(nextFrom);\n                    r.end = nextFrom + after.length;\n                    r.text = r.text.slice(0, this.ranges[rangeI - 1].to - textOffset) + after;\n                    textOffset = r.end - r.text.length;\n                }\n            }\n        }\n        return r;\n    }\n    /**\n    @internal\n    Populate this.line with the content of the next line. Skip\n    leading characters covered by composite blocks.\n    */\n    readLine() {\n        let { line } = this, { text, end } = this.scanLine(this.absoluteLineStart);\n        this.absoluteLineEnd = end;\n        line.reset(text);\n        for (; line.depth < this.stack.length; line.depth++) {\n            let cx = this.stack[line.depth], handler = this.parser.skipContextMarkup[cx.type];\n            if (!handler)\n                throw new Error(\"Unhandled block context \" + Type[cx.type]);\n            if (!handler(cx, this, line))\n                break;\n            line.forward();\n        }\n    }\n    lineChunkAt(pos) {\n        let next = this.input.chunk(pos), text;\n        if (!this.input.lineChunks) {\n            let eol = next.indexOf(\"\\n\");\n            text = eol < 0 ? next : next.slice(0, eol);\n        }\n        else {\n            text = next == \"\\n\" ? \"\" : next;\n        }\n        return pos + text.length > this.to ? text.slice(0, this.to - pos) : text;\n    }\n    /**\n    The end position of the previous line.\n    */\n    prevLineEnd() { return this.atEnd ? this.lineStart : this.lineStart - 1; }\n    /**\n    @internal\n    */\n    startContext(type, start, value = 0) {\n        this.block = CompositeBlock.create(type, value, this.lineStart + start, this.block.hash, this.lineStart + this.line.text.length);\n        this.stack.push(this.block);\n    }\n    /**\n    Start a composite block. Should only be called from [block\n    parser functions](#BlockParser.parse) that return null.\n    */\n    startComposite(type, start, value = 0) {\n        this.startContext(this.parser.getNodeType(type), start, value);\n    }\n    /**\n    @internal\n    */\n    addNode(block, from, to) {\n        if (typeof block == \"number\")\n            block = new Tree(this.parser.nodeSet.types[block], none, none, (to !== null && to !== void 0 ? to : this.prevLineEnd()) - from);\n        this.block.addChild(block, from - this.block.from);\n    }\n    /**\n    Add a block element. Can be called by [block\n    parsers](#BlockParser.parse).\n    */\n    addElement(elt) {\n        this.block.addChild(elt.toTree(this.parser.nodeSet), elt.from - this.block.from);\n    }\n    /**\n    Add a block element from a [leaf parser](#LeafBlockParser). This\n    makes sure any extra composite block markup (such as blockquote\n    markers) inside the block are also added to the syntax tree.\n    */\n    addLeafElement(leaf, elt) {\n        this.addNode(this.buffer\n            .writeElements(injectMarks(elt.children, leaf.marks), -elt.from)\n            .finish(elt.type, elt.to - elt.from), elt.from);\n    }\n    /**\n    @internal\n    */\n    finishContext() {\n        let cx = this.stack.pop();\n        let top = this.stack[this.stack.length - 1];\n        top.addChild(cx.toTree(this.parser.nodeSet), cx.from - top.from);\n        this.block = top;\n    }\n    finish() {\n        while (this.stack.length > 1)\n            this.finishContext();\n        return this.addGaps(this.block.toTree(this.parser.nodeSet, this.lineStart));\n    }\n    addGaps(tree) {\n        return this.ranges.length > 1 ?\n            injectGaps(this.ranges, 0, tree.topNode, this.ranges[0].from, this.reusePlaceholders) : tree;\n    }\n    /**\n    @internal\n    */\n    finishLeaf(leaf) {\n        for (let parser of leaf.parsers)\n            if (parser.finish(this, leaf))\n                return;\n        let inline = injectMarks(this.parser.parseInline(leaf.content, leaf.start), leaf.marks);\n        this.addNode(this.buffer\n            .writeElements(inline, -leaf.start)\n            .finish(Type.Paragraph, leaf.content.length), leaf.start);\n    }\n    elt(type, from, to, children) {\n        if (typeof type == \"string\")\n            return elt(this.parser.getNodeType(type), from, to, children);\n        return new TreeElement(type, from);\n    }\n    /**\n    @internal\n    */\n    get buffer() { return new Buffer(this.parser.nodeSet); }\n}\nfunction injectGaps(ranges, rangeI, tree, offset, dummies) {\n    let rangeEnd = ranges[rangeI].to;\n    let children = [], positions = [], start = tree.from + offset;\n    function movePastNext(upto, inclusive) {\n        while (inclusive ? upto >= rangeEnd : upto > rangeEnd) {\n            let size = ranges[rangeI + 1].from - rangeEnd;\n            offset += size;\n            upto += size;\n            rangeI++;\n            rangeEnd = ranges[rangeI].to;\n        }\n    }\n    for (let ch = tree.firstChild; ch; ch = ch.nextSibling) {\n        movePastNext(ch.from + offset, true);\n        let from = ch.from + offset, node, reuse = dummies.get(ch.tree);\n        if (reuse) {\n            node = reuse;\n        }\n        else if (ch.to + offset > rangeEnd) {\n            node = injectGaps(ranges, rangeI, ch, offset, dummies);\n            movePastNext(ch.to + offset, false);\n        }\n        else {\n            node = ch.toTree();\n        }\n        children.push(node);\n        positions.push(from - start);\n    }\n    movePastNext(tree.to + offset, false);\n    return new Tree(tree.type, children, positions, tree.to + offset - start, tree.tree ? tree.tree.propValues : undefined);\n}\n/**\nA Markdown parser configuration.\n*/\nclass MarkdownParser extends Parser {\n    /**\n    @internal\n    */\n    constructor(\n    /**\n    The parser's syntax [node\n    types](https://lezer.codemirror.net/docs/ref/#common.NodeSet).\n    */\n    nodeSet, \n    /**\n    @internal\n    */\n    blockParsers, \n    /**\n    @internal\n    */\n    leafBlockParsers, \n    /**\n    @internal\n    */\n    blockNames, \n    /**\n    @internal\n    */\n    endLeafBlock, \n    /**\n    @internal\n    */\n    skipContextMarkup, \n    /**\n    @internal\n    */\n    inlineParsers, \n    /**\n    @internal\n    */\n    inlineNames, \n    /**\n    @internal\n    */\n    wrappers) {\n        super();\n        this.nodeSet = nodeSet;\n        this.blockParsers = blockParsers;\n        this.leafBlockParsers = leafBlockParsers;\n        this.blockNames = blockNames;\n        this.endLeafBlock = endLeafBlock;\n        this.skipContextMarkup = skipContextMarkup;\n        this.inlineParsers = inlineParsers;\n        this.inlineNames = inlineNames;\n        this.wrappers = wrappers;\n        /**\n        @internal\n        */\n        this.nodeTypes = Object.create(null);\n        for (let t of nodeSet.types)\n            this.nodeTypes[t.name] = t.id;\n    }\n    createParse(input, fragments, ranges) {\n        let parse = new BlockContext(this, input, fragments, ranges);\n        for (let w of this.wrappers)\n            parse = w(parse, input, fragments, ranges);\n        return parse;\n    }\n    /**\n    Reconfigure the parser.\n    */\n    configure(spec) {\n        let config = resolveConfig(spec);\n        if (!config)\n            return this;\n        let { nodeSet, skipContextMarkup } = this;\n        let blockParsers = this.blockParsers.slice(), leafBlockParsers = this.leafBlockParsers.slice(), blockNames = this.blockNames.slice(), inlineParsers = this.inlineParsers.slice(), inlineNames = this.inlineNames.slice(), endLeafBlock = this.endLeafBlock.slice(), wrappers = this.wrappers;\n        if (nonEmpty(config.defineNodes)) {\n            skipContextMarkup = Object.assign({}, skipContextMarkup);\n            let nodeTypes = nodeSet.types.slice(), styles;\n            for (let s of config.defineNodes) {\n                let { name, block, composite, style } = typeof s == \"string\" ? { name: s } : s;\n                if (nodeTypes.some(t => t.name == name))\n                    continue;\n                if (composite)\n                    skipContextMarkup[nodeTypes.length] =\n                        (bl, cx, line) => composite(cx, line, bl.value);\n                let id = nodeTypes.length;\n                let group = composite ? [\"Block\", \"BlockContext\"] : !block ? undefined\n                    : id >= Type.ATXHeading1 && id <= Type.SetextHeading2 ? [\"Block\", \"LeafBlock\", \"Heading\"] : [\"Block\", \"LeafBlock\"];\n                nodeTypes.push(NodeType.define({\n                    id,\n                    name,\n                    props: group && [[NodeProp.group, group]]\n                }));\n                if (style) {\n                    if (!styles)\n                        styles = {};\n                    if (Array.isArray(style) || style instanceof Tag)\n                        styles[name] = style;\n                    else\n                        Object.assign(styles, style);\n                }\n            }\n            nodeSet = new NodeSet(nodeTypes);\n            if (styles)\n                nodeSet = nodeSet.extend(styleTags(styles));\n        }\n        if (nonEmpty(config.props))\n            nodeSet = nodeSet.extend(...config.props);\n        if (nonEmpty(config.remove)) {\n            for (let rm of config.remove) {\n                let block = this.blockNames.indexOf(rm), inline = this.inlineNames.indexOf(rm);\n                if (block > -1)\n                    blockParsers[block] = leafBlockParsers[block] = undefined;\n                if (inline > -1)\n                    inlineParsers[inline] = undefined;\n            }\n        }\n        if (nonEmpty(config.parseBlock)) {\n            for (let spec of config.parseBlock) {\n                let found = blockNames.indexOf(spec.name);\n                if (found > -1) {\n                    blockParsers[found] = spec.parse;\n                    leafBlockParsers[found] = spec.leaf;\n                }\n                else {\n                    let pos = spec.before ? findName(blockNames, spec.before)\n                        : spec.after ? findName(blockNames, spec.after) + 1 : blockNames.length - 1;\n                    blockParsers.splice(pos, 0, spec.parse);\n                    leafBlockParsers.splice(pos, 0, spec.leaf);\n                    blockNames.splice(pos, 0, spec.name);\n                }\n                if (spec.endLeaf)\n                    endLeafBlock.push(spec.endLeaf);\n            }\n        }\n        if (nonEmpty(config.parseInline)) {\n            for (let spec of config.parseInline) {\n                let found = inlineNames.indexOf(spec.name);\n                if (found > -1) {\n                    inlineParsers[found] = spec.parse;\n                }\n                else {\n                    let pos = spec.before ? findName(inlineNames, spec.before)\n                        : spec.after ? findName(inlineNames, spec.after) + 1 : inlineNames.length - 1;\n                    inlineParsers.splice(pos, 0, spec.parse);\n                    inlineNames.splice(pos, 0, spec.name);\n                }\n            }\n        }\n        if (config.wrap)\n            wrappers = wrappers.concat(config.wrap);\n        return new MarkdownParser(nodeSet, blockParsers, leafBlockParsers, blockNames, endLeafBlock, skipContextMarkup, inlineParsers, inlineNames, wrappers);\n    }\n    /**\n    @internal\n    */\n    getNodeType(name) {\n        let found = this.nodeTypes[name];\n        if (found == null)\n            throw new RangeError(`Unknown node type '${name}'`);\n        return found;\n    }\n    /**\n    Parse the given piece of inline text at the given offset,\n    returning an array of [`Element`](#Element) objects representing\n    the inline content.\n    */\n    parseInline(text, offset) {\n        let cx = new InlineContext(this, text, offset);\n        outer: for (let pos = offset; pos < cx.end;) {\n            let next = cx.char(pos);\n            for (let token of this.inlineParsers)\n                if (token) {\n                    let result = token(cx, next, pos);\n                    if (result >= 0) {\n                        pos = result;\n                        continue outer;\n                    }\n                }\n            pos++;\n        }\n        return cx.resolveMarkers(0);\n    }\n}\nfunction nonEmpty(a) {\n    return a != null && a.length > 0;\n}\nfunction resolveConfig(spec) {\n    if (!Array.isArray(spec))\n        return spec;\n    if (spec.length == 0)\n        return null;\n    let conf = resolveConfig(spec[0]);\n    if (spec.length == 1)\n        return conf;\n    let rest = resolveConfig(spec.slice(1));\n    if (!rest || !conf)\n        return conf || rest;\n    let conc = (a, b) => (a || none).concat(b || none);\n    let wrapA = conf.wrap, wrapB = rest.wrap;\n    return {\n        props: conc(conf.props, rest.props),\n        defineNodes: conc(conf.defineNodes, rest.defineNodes),\n        parseBlock: conc(conf.parseBlock, rest.parseBlock),\n        parseInline: conc(conf.parseInline, rest.parseInline),\n        remove: conc(conf.remove, rest.remove),\n        wrap: !wrapA ? wrapB : !wrapB ? wrapA :\n            (inner, input, fragments, ranges) => wrapA(wrapB(inner, input, fragments, ranges), input, fragments, ranges)\n    };\n}\nfunction findName(names, name) {\n    let found = names.indexOf(name);\n    if (found < 0)\n        throw new RangeError(`Position specified relative to unknown parser ${name}`);\n    return found;\n}\nlet nodeTypes = [NodeType.none];\nfor (let i = 1, name; name = Type[i]; i++) {\n    nodeTypes[i] = NodeType.define({\n        id: i,\n        name,\n        props: i >= Type.Escape ? [] : [[NodeProp.group, i in DefaultSkipMarkup ? [\"Block\", \"BlockContext\"] : [\"Block\", \"LeafBlock\"]]],\n        top: name == \"Document\"\n    });\n}\nconst none = [];\nclass Buffer {\n    constructor(nodeSet) {\n        this.nodeSet = nodeSet;\n        this.content = [];\n        this.nodes = [];\n    }\n    write(type, from, to, children = 0) {\n        this.content.push(type, from, to, 4 + children * 4);\n        return this;\n    }\n    writeElements(elts, offset = 0) {\n        for (let e of elts)\n            e.writeTo(this, offset);\n        return this;\n    }\n    finish(type, length) {\n        return Tree.build({\n            buffer: this.content,\n            nodeSet: this.nodeSet,\n            reused: this.nodes,\n            topID: type,\n            length\n        });\n    }\n}\n/**\nElements are used to compose syntax nodes during parsing.\n*/\nclass Element {\n    /**\n    @internal\n    */\n    constructor(\n    /**\n    The node's\n    [id](https://lezer.codemirror.net/docs/ref/#common.NodeType.id).\n    */\n    type, \n    /**\n    The start of the node, as an offset from the start of the document.\n    */\n    from, \n    /**\n    The end of the node.\n    */\n    to, \n    /**\n    The node's child nodes @internal\n    */\n    children = none) {\n        this.type = type;\n        this.from = from;\n        this.to = to;\n        this.children = children;\n    }\n    /**\n    @internal\n    */\n    writeTo(buf, offset) {\n        let startOff = buf.content.length;\n        buf.writeElements(this.children, offset);\n        buf.content.push(this.type, this.from + offset, this.to + offset, buf.content.length + 4 - startOff);\n    }\n    /**\n    @internal\n    */\n    toTree(nodeSet) {\n        return new Buffer(nodeSet).writeElements(this.children, -this.from).finish(this.type, this.to - this.from);\n    }\n}\nclass TreeElement {\n    constructor(tree, from) {\n        this.tree = tree;\n        this.from = from;\n    }\n    get to() { return this.from + this.tree.length; }\n    get type() { return this.tree.type.id; }\n    get children() { return none; }\n    writeTo(buf, offset) {\n        buf.nodes.push(this.tree);\n        buf.content.push(buf.nodes.length - 1, this.from + offset, this.to + offset, -1);\n    }\n    toTree() { return this.tree; }\n}\nfunction elt(type, from, to, children) {\n    return new Element(type, from, to, children);\n}\nconst EmphasisUnderscore = { resolve: \"Emphasis\", mark: \"EmphasisMark\" };\nconst EmphasisAsterisk = { resolve: \"Emphasis\", mark: \"EmphasisMark\" };\nconst LinkStart = {}, ImageStart = {};\nclass InlineDelimiter {\n    constructor(type, from, to, side) {\n        this.type = type;\n        this.from = from;\n        this.to = to;\n        this.side = side;\n    }\n}\nconst Escapable = \"!\\\"#$%&'()*+,-./:;<=>?@[\\\\]^_`{|}~\";\nlet Punctuation = /[!\"#$%&'()*+,\\-.\\/:;<=>?@\\[\\\\\\]^_`{|}~\\xA1\\u2010-\\u2027]/;\ntry {\n    Punctuation = new RegExp(\"[\\\\p{S}|\\\\p{P}]\", \"u\");\n}\ncatch (_) { }\nconst DefaultInline = {\n    Escape(cx, next, start) {\n        if (next != 92 /* '\\\\' */ || start == cx.end - 1)\n            return -1;\n        let escaped = cx.char(start + 1);\n        for (let i = 0; i < Escapable.length; i++)\n            if (Escapable.charCodeAt(i) == escaped)\n                return cx.append(elt(Type.Escape, start, start + 2));\n        return -1;\n    },\n    Entity(cx, next, start) {\n        if (next != 38 /* '&' */)\n            return -1;\n        let m = /^(?:#\\d+|#x[a-f\\d]+|\\w+);/i.exec(cx.slice(start + 1, start + 31));\n        return m ? cx.append(elt(Type.Entity, start, start + 1 + m[0].length)) : -1;\n    },\n    InlineCode(cx, next, start) {\n        if (next != 96 /* '`' */ || start && cx.char(start - 1) == 96)\n            return -1;\n        let pos = start + 1;\n        while (pos < cx.end && cx.char(pos) == 96)\n            pos++;\n        let size = pos - start, curSize = 0;\n        for (; pos < cx.end; pos++) {\n            if (cx.char(pos) == 96) {\n                curSize++;\n                if (curSize == size && cx.char(pos + 1) != 96)\n                    return cx.append(elt(Type.InlineCode, start, pos + 1, [\n                        elt(Type.CodeMark, start, start + size),\n                        elt(Type.CodeMark, pos + 1 - size, pos + 1)\n                    ]));\n            }\n            else {\n                curSize = 0;\n            }\n        }\n        return -1;\n    },\n    HTMLTag(cx, next, start) {\n        if (next != 60 /* '<' */ || start == cx.end - 1)\n            return -1;\n        let after = cx.slice(start + 1, cx.end);\n        let url = /^(?:[a-z][-\\w+.]+:[^\\s>]+|[a-z\\d.!#$%&'*+/=?^_`{|}~-]+@[a-z\\d](?:[a-z\\d-]{0,61}[a-z\\d])?(?:\\.[a-z\\d](?:[a-z\\d-]{0,61}[a-z\\d])?)*)>/i.exec(after);\n        if (url) {\n            return cx.append(elt(Type.Autolink, start, start + 1 + url[0].length, [\n                elt(Type.LinkMark, start, start + 1),\n                // url[0] includes the closing bracket, so exclude it from this slice\n                elt(Type.URL, start + 1, start + url[0].length),\n                elt(Type.LinkMark, start + url[0].length, start + 1 + url[0].length)\n            ]));\n        }\n        let comment = /^!--[^>](?:-[^-]|[^-])*?-->/i.exec(after);\n        if (comment)\n            return cx.append(elt(Type.Comment, start, start + 1 + comment[0].length));\n        let procInst = /^\\?[^]*?\\?>/.exec(after);\n        if (procInst)\n            return cx.append(elt(Type.ProcessingInstruction, start, start + 1 + procInst[0].length));\n        let m = /^(?:![A-Z][^]*?>|!\\[CDATA\\[[^]*?\\]\\]>|\\/\\s*[a-zA-Z][\\w-]*\\s*>|\\s*[a-zA-Z][\\w-]*(\\s+[a-zA-Z:_][\\w-.:]*(?:\\s*=\\s*(?:[^\\s\"'=<>`]+|'[^']*'|\"[^\"]*\"))?)*\\s*(\\/\\s*)?>)/.exec(after);\n        if (!m)\n            return -1;\n        return cx.append(elt(Type.HTMLTag, start, start + 1 + m[0].length));\n    },\n    Emphasis(cx, next, start) {\n        if (next != 95 && next != 42)\n            return -1;\n        let pos = start + 1;\n        while (cx.char(pos) == next)\n            pos++;\n        let before = cx.slice(start - 1, start), after = cx.slice(pos, pos + 1);\n        let pBefore = Punctuation.test(before), pAfter = Punctuation.test(after);\n        let sBefore = /\\s|^$/.test(before), sAfter = /\\s|^$/.test(after);\n        let leftFlanking = !sAfter && (!pAfter || sBefore || pBefore);\n        let rightFlanking = !sBefore && (!pBefore || sAfter || pAfter);\n        let canOpen = leftFlanking && (next == 42 || !rightFlanking || pBefore);\n        let canClose = rightFlanking && (next == 42 || !leftFlanking || pAfter);\n        return cx.append(new InlineDelimiter(next == 95 ? EmphasisUnderscore : EmphasisAsterisk, start, pos, (canOpen ? 1 /* Mark.Open */ : 0 /* Mark.None */) | (canClose ? 2 /* Mark.Close */ : 0 /* Mark.None */)));\n    },\n    HardBreak(cx, next, start) {\n        if (next == 92 /* '\\\\' */ && cx.char(start + 1) == 10 /* '\\n' */)\n            return cx.append(elt(Type.HardBreak, start, start + 2));\n        if (next == 32) {\n            let pos = start + 1;\n            while (cx.char(pos) == 32)\n                pos++;\n            if (cx.char(pos) == 10 && pos >= start + 2)\n                return cx.append(elt(Type.HardBreak, start, pos + 1));\n        }\n        return -1;\n    },\n    Link(cx, next, start) {\n        return next == 91 /* '[' */ ? cx.append(new InlineDelimiter(LinkStart, start, start + 1, 1 /* Mark.Open */)) : -1;\n    },\n    Image(cx, next, start) {\n        return next == 33 /* '!' */ && cx.char(start + 1) == 91 /* '[' */\n            ? cx.append(new InlineDelimiter(ImageStart, start, start + 2, 1 /* Mark.Open */)) : -1;\n    },\n    LinkEnd(cx, next, start) {\n        if (next != 93 /* ']' */)\n            return -1;\n        // Scanning back to the next link/image start marker\n        for (let i = cx.parts.length - 1; i >= 0; i--) {\n            let part = cx.parts[i];\n            if (part instanceof InlineDelimiter && (part.type == LinkStart || part.type == ImageStart)) {\n                // If this one has been set invalid (because it would produce\n                // a nested link) or there's no valid link here ignore both.\n                if (!part.side || cx.skipSpace(part.to) == start && !/[(\\[]/.test(cx.slice(start + 1, start + 2))) {\n                    cx.parts[i] = null;\n                    return -1;\n                }\n                // Finish the content and replace the entire range in\n                // this.parts with the link/image node.\n                let content = cx.takeContent(i);\n                let link = cx.parts[i] = finishLink(cx, content, part.type == LinkStart ? Type.Link : Type.Image, part.from, start + 1);\n                // Set any open-link markers before this link to invalid.\n                if (part.type == LinkStart)\n                    for (let j = 0; j < i; j++) {\n                        let p = cx.parts[j];\n                        if (p instanceof InlineDelimiter && p.type == LinkStart)\n                            p.side = 0 /* Mark.None */;\n                    }\n                return link.to;\n            }\n        }\n        return -1;\n    }\n};\nfunction finishLink(cx, content, type, start, startPos) {\n    let { text } = cx, next = cx.char(startPos), endPos = startPos;\n    content.unshift(elt(Type.LinkMark, start, start + (type == Type.Image ? 2 : 1)));\n    content.push(elt(Type.LinkMark, startPos - 1, startPos));\n    if (next == 40 /* '(' */) {\n        let pos = cx.skipSpace(startPos + 1);\n        let dest = parseURL(text, pos - cx.offset, cx.offset), title;\n        if (dest) {\n            pos = cx.skipSpace(dest.to);\n            // The destination and title must be separated by whitespace\n            if (pos != dest.to) {\n                title = parseLinkTitle(text, pos - cx.offset, cx.offset);\n                if (title)\n                    pos = cx.skipSpace(title.to);\n            }\n        }\n        if (cx.char(pos) == 41 /* ')' */) {\n            content.push(elt(Type.LinkMark, startPos, startPos + 1));\n            endPos = pos + 1;\n            if (dest)\n                content.push(dest);\n            if (title)\n                content.push(title);\n            content.push(elt(Type.LinkMark, pos, endPos));\n        }\n    }\n    else if (next == 91 /* '[' */) {\n        let label = parseLinkLabel(text, startPos - cx.offset, cx.offset, false);\n        if (label) {\n            content.push(label);\n            endPos = label.to;\n        }\n    }\n    return elt(type, start, endPos, content);\n}\n// These return `null` when falling off the end of the input, `false`\n// when parsing fails otherwise (for use in the incremental link\n// reference parser).\nfunction parseURL(text, start, offset) {\n    let next = text.charCodeAt(start);\n    if (next == 60 /* '<' */) {\n        for (let pos = start + 1; pos < text.length; pos++) {\n            let ch = text.charCodeAt(pos);\n            if (ch == 62 /* '>' */)\n                return elt(Type.URL, start + offset, pos + 1 + offset);\n            if (ch == 60 || ch == 10 /* '<\\n' */)\n                return false;\n        }\n        return null;\n    }\n    else {\n        let depth = 0, pos = start;\n        for (let escaped = false; pos < text.length; pos++) {\n            let ch = text.charCodeAt(pos);\n            if (space(ch)) {\n                break;\n            }\n            else if (escaped) {\n                escaped = false;\n            }\n            else if (ch == 40 /* '(' */) {\n                depth++;\n            }\n            else if (ch == 41 /* ')' */) {\n                if (!depth)\n                    break;\n                depth--;\n            }\n            else if (ch == 92 /* '\\\\' */) {\n                escaped = true;\n            }\n        }\n        return pos > start ? elt(Type.URL, start + offset, pos + offset) : pos == text.length ? null : false;\n    }\n}\nfunction parseLinkTitle(text, start, offset) {\n    let next = text.charCodeAt(start);\n    if (next != 39 && next != 34 && next != 40 /* '\"\\'(' */)\n        return false;\n    let end = next == 40 ? 41 : next;\n    for (let pos = start + 1, escaped = false; pos < text.length; pos++) {\n        let ch = text.charCodeAt(pos);\n        if (escaped)\n            escaped = false;\n        else if (ch == end)\n            return elt(Type.LinkTitle, start + offset, pos + 1 + offset);\n        else if (ch == 92 /* '\\\\' */)\n            escaped = true;\n    }\n    return null;\n}\nfunction parseLinkLabel(text, start, offset, requireNonWS) {\n    for (let escaped = false, pos = start + 1, end = Math.min(text.length, pos + 999); pos < end; pos++) {\n        let ch = text.charCodeAt(pos);\n        if (escaped)\n            escaped = false;\n        else if (ch == 93 /* ']' */)\n            return requireNonWS ? false : elt(Type.LinkLabel, start + offset, pos + 1 + offset);\n        else {\n            if (requireNonWS && !space(ch))\n                requireNonWS = false;\n            if (ch == 91 /* '[' */)\n                return false;\n            else if (ch == 92 /* '\\\\' */)\n                escaped = true;\n        }\n    }\n    return null;\n}\n/**\nInline parsing functions get access to this context, and use it to\nread the content and emit syntax nodes.\n*/\nclass InlineContext {\n    /**\n    @internal\n    */\n    constructor(\n    /**\n    The parser that is being used.\n    */\n    parser, \n    /**\n    The text of this inline section.\n    */\n    text, \n    /**\n    The starting offset of the section in the document.\n    */\n    offset) {\n        this.parser = parser;\n        this.text = text;\n        this.offset = offset;\n        /**\n        @internal\n        */\n        this.parts = [];\n    }\n    /**\n    Get the character code at the given (document-relative)\n    position.\n    */\n    char(pos) { return pos >= this.end ? -1 : this.text.charCodeAt(pos - this.offset); }\n    /**\n    The position of the end of this inline section.\n    */\n    get end() { return this.offset + this.text.length; }\n    /**\n    Get a substring of this inline section. Again uses\n    document-relative positions.\n    */\n    slice(from, to) { return this.text.slice(from - this.offset, to - this.offset); }\n    /**\n    @internal\n    */\n    append(elt) {\n        this.parts.push(elt);\n        return elt.to;\n    }\n    /**\n    Add a [delimiter](#DelimiterType) at this given position. `open`\n    and `close` indicate whether this delimiter is opening, closing,\n    or both. Returns the end of the delimiter, for convenient\n    returning from [parse functions](#InlineParser.parse).\n    */\n    addDelimiter(type, from, to, open, close) {\n        return this.append(new InlineDelimiter(type, from, to, (open ? 1 /* Mark.Open */ : 0 /* Mark.None */) | (close ? 2 /* Mark.Close */ : 0 /* Mark.None */)));\n    }\n    /**\n    Returns true when there is an unmatched link or image opening\n    token before the current position.\n    */\n    get hasOpenLink() {\n        for (let i = this.parts.length - 1; i >= 0; i--) {\n            let part = this.parts[i];\n            if (part instanceof InlineDelimiter && (part.type == LinkStart || part.type == ImageStart))\n                return true;\n        }\n        return false;\n    }\n    /**\n    Add an inline element. Returns the end of the element.\n    */\n    addElement(elt) {\n        return this.append(elt);\n    }\n    /**\n    Resolve markers between this.parts.length and from, wrapping matched markers in the\n    appropriate node and updating the content of this.parts. @internal\n    */\n    resolveMarkers(from) {\n        // Scan forward, looking for closing tokens\n        for (let i = from; i < this.parts.length; i++) {\n            let close = this.parts[i];\n            if (!(close instanceof InlineDelimiter && close.type.resolve && (close.side & 2 /* Mark.Close */)))\n                continue;\n            let emp = close.type == EmphasisUnderscore || close.type == EmphasisAsterisk;\n            let closeSize = close.to - close.from;\n            let open, j = i - 1;\n            // Continue scanning for a matching opening token\n            for (; j >= from; j--) {\n                let part = this.parts[j];\n                if (part instanceof InlineDelimiter && (part.side & 1 /* Mark.Open */) && part.type == close.type &&\n                    // Ignore emphasis delimiters where the character count doesn't match\n                    !(emp && ((close.side & 1 /* Mark.Open */) || (part.side & 2 /* Mark.Close */)) &&\n                        (part.to - part.from + closeSize) % 3 == 0 && ((part.to - part.from) % 3 || closeSize % 3))) {\n                    open = part;\n                    break;\n                }\n            }\n            if (!open)\n                continue;\n            let type = close.type.resolve, content = [];\n            let start = open.from, end = close.to;\n            // Emphasis marker effect depends on the character count. Size consumed is minimum of the two\n            // markers.\n            if (emp) {\n                let size = Math.min(2, open.to - open.from, closeSize);\n                start = open.to - size;\n                end = close.from + size;\n                type = size == 1 ? \"Emphasis\" : \"StrongEmphasis\";\n            }\n            // Move the covered region into content, optionally adding marker nodes\n            if (open.type.mark)\n                content.push(this.elt(open.type.mark, start, open.to));\n            for (let k = j + 1; k < i; k++) {\n                if (this.parts[k] instanceof Element)\n                    content.push(this.parts[k]);\n                this.parts[k] = null;\n            }\n            if (close.type.mark)\n                content.push(this.elt(close.type.mark, close.from, end));\n            let element = this.elt(type, start, end, content);\n            // If there are leftover emphasis marker characters, shrink the close/open markers. Otherwise, clear them.\n            this.parts[j] = emp && open.from != start ? new InlineDelimiter(open.type, open.from, start, open.side) : null;\n            let keep = this.parts[i] = emp && close.to != end ? new InlineDelimiter(close.type, end, close.to, close.side) : null;\n            // Insert the new element in this.parts\n            if (keep)\n                this.parts.splice(i, 0, element);\n            else\n                this.parts[i] = element;\n        }\n        // Collect the elements remaining in this.parts into an array.\n        let result = [];\n        for (let i = from; i < this.parts.length; i++) {\n            let part = this.parts[i];\n            if (part instanceof Element)\n                result.push(part);\n        }\n        return result;\n    }\n    /**\n    Find an opening delimiter of the given type. Returns `null` if\n    no delimiter is found, or an index that can be passed to\n    [`takeContent`](#InlineContext.takeContent) otherwise.\n    */\n    findOpeningDelimiter(type) {\n        for (let i = this.parts.length - 1; i >= 0; i--) {\n            let part = this.parts[i];\n            if (part instanceof InlineDelimiter && part.type == type)\n                return i;\n        }\n        return null;\n    }\n    /**\n    Remove all inline elements and delimiters starting from the\n    given index (which you should get from\n    [`findOpeningDelimiter`](#InlineContext.findOpeningDelimiter),\n    resolve delimiters inside of them, and return them as an array\n    of elements.\n    */\n    takeContent(startIndex) {\n        let content = this.resolveMarkers(startIndex);\n        this.parts.length = startIndex;\n        return content;\n    }\n    /**\n    Skip space after the given (document) position, returning either\n    the position of the next non-space character or the end of the\n    section.\n    */\n    skipSpace(from) { return skipSpace(this.text, from - this.offset) + this.offset; }\n    elt(type, from, to, children) {\n        if (typeof type == \"string\")\n            return elt(this.parser.getNodeType(type), from, to, children);\n        return new TreeElement(type, from);\n    }\n}\nfunction injectMarks(elements, marks) {\n    if (!marks.length)\n        return elements;\n    if (!elements.length)\n        return marks;\n    let elts = elements.slice(), eI = 0;\n    for (let mark of marks) {\n        while (eI < elts.length && elts[eI].to < mark.to)\n            eI++;\n        if (eI < elts.length && elts[eI].from < mark.from) {\n            let e = elts[eI];\n            if (e instanceof Element)\n                elts[eI] = new Element(e.type, e.from, e.to, injectMarks(e.children, [mark]));\n        }\n        else {\n            elts.splice(eI++, 0, mark);\n        }\n    }\n    return elts;\n}\n// These are blocks that can span blank lines, and should thus only be\n// reused if their next sibling is also being reused.\nconst NotLast = [Type.CodeBlock, Type.ListItem, Type.OrderedList, Type.BulletList];\nclass FragmentCursor {\n    constructor(fragments, input) {\n        this.fragments = fragments;\n        this.input = input;\n        // Index into fragment array\n        this.i = 0;\n        // Active fragment\n        this.fragment = null;\n        this.fragmentEnd = -1;\n        // Cursor into the current fragment, if any. When `moveTo` returns\n        // true, this points at the first block after `pos`.\n        this.cursor = null;\n        if (fragments.length)\n            this.fragment = fragments[this.i++];\n    }\n    nextFragment() {\n        this.fragment = this.i < this.fragments.length ? this.fragments[this.i++] : null;\n        this.cursor = null;\n        this.fragmentEnd = -1;\n    }\n    moveTo(pos, lineStart) {\n        while (this.fragment && this.fragment.to <= pos)\n            this.nextFragment();\n        if (!this.fragment || this.fragment.from > (pos ? pos - 1 : 0))\n            return false;\n        if (this.fragmentEnd < 0) {\n            let end = this.fragment.to;\n            while (end > 0 && this.input.read(end - 1, end) != \"\\n\")\n                end--;\n            this.fragmentEnd = end ? end - 1 : 0;\n        }\n        let c = this.cursor;\n        if (!c) {\n            c = this.cursor = this.fragment.tree.cursor();\n            c.firstChild();\n        }\n        let rPos = pos + this.fragment.offset;\n        while (c.to <= rPos)\n            if (!c.parent())\n                return false;\n        for (;;) {\n            if (c.from >= rPos)\n                return this.fragment.from <= lineStart;\n            if (!c.childAfter(rPos))\n                return false;\n        }\n    }\n    matches(hash) {\n        let tree = this.cursor.tree;\n        return tree && tree.prop(NodeProp.contextHash) == hash;\n    }\n    takeNodes(cx) {\n        let cur = this.cursor, off = this.fragment.offset, fragEnd = this.fragmentEnd - (this.fragment.openEnd ? 1 : 0);\n        let start = cx.absoluteLineStart, end = start, blockI = cx.block.children.length;\n        let prevEnd = end, prevI = blockI;\n        for (;;) {\n            if (cur.to - off > fragEnd) {\n                if (cur.type.isAnonymous && cur.firstChild())\n                    continue;\n                break;\n            }\n            let pos = toRelative(cur.from - off, cx.ranges);\n            if (cur.to - off <= cx.ranges[cx.rangeI].to) { // Fits in current range\n                cx.addNode(cur.tree, pos);\n            }\n            else {\n                let dummy = new Tree(cx.parser.nodeSet.types[Type.Paragraph], [], [], 0, cx.block.hashProp);\n                cx.reusePlaceholders.set(dummy, cur.tree);\n                cx.addNode(dummy, pos);\n            }\n            // Taken content must always end in a block, because incremental\n            // parsing happens on block boundaries. Never stop directly\n            // after an indented code block, since those can continue after\n            // any number of blank lines.\n            if (cur.type.is(\"Block\")) {\n                if (NotLast.indexOf(cur.type.id) < 0) {\n                    end = cur.to - off;\n                    blockI = cx.block.children.length;\n                }\n                else {\n                    end = prevEnd;\n                    blockI = prevI;\n                    prevEnd = cur.to - off;\n                    prevI = cx.block.children.length;\n                }\n            }\n            if (!cur.nextSibling())\n                break;\n        }\n        while (cx.block.children.length > blockI) {\n            cx.block.children.pop();\n            cx.block.positions.pop();\n        }\n        return end - start;\n    }\n}\n// Convert an input-stream-relative position to a\n// Markdown-doc-relative position by subtracting the size of all input\n// gaps before `abs`.\nfunction toRelative(abs, ranges) {\n    let pos = abs;\n    for (let i = 1; i < ranges.length; i++) {\n        let gapFrom = ranges[i - 1].to, gapTo = ranges[i].from;\n        if (gapFrom < abs)\n            pos -= gapTo - gapFrom;\n    }\n    return pos;\n}\nconst markdownHighlighting = styleTags({\n    \"Blockquote/...\": tags.quote,\n    HorizontalRule: tags.contentSeparator,\n    \"ATXHeading1/... SetextHeading1/...\": tags.heading1,\n    \"ATXHeading2/... SetextHeading2/...\": tags.heading2,\n    \"ATXHeading3/...\": tags.heading3,\n    \"ATXHeading4/...\": tags.heading4,\n    \"ATXHeading5/...\": tags.heading5,\n    \"ATXHeading6/...\": tags.heading6,\n    \"Comment CommentBlock\": tags.comment,\n    Escape: tags.escape,\n    Entity: tags.character,\n    \"Emphasis/...\": tags.emphasis,\n    \"StrongEmphasis/...\": tags.strong,\n    \"Link/... Image/...\": tags.link,\n    \"OrderedList/... BulletList/...\": tags.list,\n    \"BlockQuote/...\": tags.quote,\n    \"InlineCode CodeText\": tags.monospace,\n    \"URL Autolink\": tags.url,\n    \"HeaderMark HardBreak QuoteMark ListMark LinkMark EmphasisMark CodeMark\": tags.processingInstruction,\n    \"CodeInfo LinkLabel\": tags.labelName,\n    LinkTitle: tags.string,\n    Paragraph: tags.content\n});\n/**\nThe default CommonMark parser.\n*/\nconst parser = new MarkdownParser(new NodeSet(nodeTypes).extend(markdownHighlighting), Object.keys(DefaultBlockParsers).map(n => DefaultBlockParsers[n]), Object.keys(DefaultBlockParsers).map(n => DefaultLeafBlocks[n]), Object.keys(DefaultBlockParsers), DefaultEndLeaf, DefaultSkipMarkup, Object.keys(DefaultInline).map(n => DefaultInline[n]), Object.keys(DefaultInline), []);\n\nfunction leftOverSpace(node, from, to) {\n    let ranges = [];\n    for (let n = node.firstChild, pos = from;; n = n.nextSibling) {\n        let nextPos = n ? n.from : to;\n        if (nextPos > pos)\n            ranges.push({ from: pos, to: nextPos });\n        if (!n)\n            break;\n        pos = n.to;\n    }\n    return ranges;\n}\n/**\nCreate a Markdown extension to enable nested parsing on code\nblocks and/or embedded HTML.\n*/\nfunction parseCode(config) {\n    let { codeParser, htmlParser } = config;\n    let wrap = parseMixed((node, input) => {\n        let id = node.type.id;\n        if (codeParser && (id == Type.CodeBlock || id == Type.FencedCode)) {\n            let info = \"\";\n            if (id == Type.FencedCode) {\n                let infoNode = node.node.getChild(Type.CodeInfo);\n                if (infoNode)\n                    info = input.read(infoNode.from, infoNode.to);\n            }\n            let parser = codeParser(info);\n            if (parser)\n                return { parser, overlay: node => node.type.id == Type.CodeText };\n        }\n        else if (htmlParser && (id == Type.HTMLBlock || id == Type.HTMLTag || id == Type.CommentBlock)) {\n            return { parser: htmlParser, overlay: leftOverSpace(node.node, node.from, node.to) };\n        }\n        return null;\n    });\n    return { wrap };\n}\n\nconst StrikethroughDelim = { resolve: \"Strikethrough\", mark: \"StrikethroughMark\" };\n/**\nAn extension that implements\n[GFM-style](https://github.github.com/gfm/#strikethrough-extension-)\nStrikethrough syntax using `~~` delimiters.\n*/\nconst Strikethrough = {\n    defineNodes: [{\n            name: \"Strikethrough\",\n            style: { \"Strikethrough/...\": tags.strikethrough }\n        }, {\n            name: \"StrikethroughMark\",\n            style: tags.processingInstruction\n        }],\n    parseInline: [{\n            name: \"Strikethrough\",\n            parse(cx, next, pos) {\n                if (next != 126 /* '~' */ || cx.char(pos + 1) != 126 || cx.char(pos + 2) == 126)\n                    return -1;\n                let before = cx.slice(pos - 1, pos), after = cx.slice(pos + 2, pos + 3);\n                let sBefore = /\\s|^$/.test(before), sAfter = /\\s|^$/.test(after);\n                let pBefore = Punctuation.test(before), pAfter = Punctuation.test(after);\n                return cx.addDelimiter(StrikethroughDelim, pos, pos + 2, !sAfter && (!pAfter || sBefore || pBefore), !sBefore && (!pBefore || sAfter || pAfter));\n            },\n            after: \"Emphasis\"\n        }]\n};\n// Parse a line as a table row and return the row count. When `elts`\n// is given, push syntax elements for the content onto it.\nfunction parseRow(cx, line, startI = 0, elts, offset = 0) {\n    let count = 0, first = true, cellStart = -1, cellEnd = -1, esc = false;\n    let parseCell = () => {\n        elts.push(cx.elt(\"TableCell\", offset + cellStart, offset + cellEnd, cx.parser.parseInline(line.slice(cellStart, cellEnd), offset + cellStart)));\n    };\n    for (let i = startI; i < line.length; i++) {\n        let next = line.charCodeAt(i);\n        if (next == 124 /* '|' */ && !esc) {\n            if (!first || cellStart > -1)\n                count++;\n            first = false;\n            if (elts) {\n                if (cellStart > -1)\n                    parseCell();\n                elts.push(cx.elt(\"TableDelimiter\", i + offset, i + offset + 1));\n            }\n            cellStart = cellEnd = -1;\n        }\n        else if (esc || next != 32 && next != 9) {\n            if (cellStart < 0)\n                cellStart = i;\n            cellEnd = i + 1;\n        }\n        esc = !esc && next == 92;\n    }\n    if (cellStart > -1) {\n        count++;\n        if (elts)\n            parseCell();\n    }\n    return count;\n}\nfunction hasPipe(str, start) {\n    for (let i = start; i < str.length; i++) {\n        let next = str.charCodeAt(i);\n        if (next == 124 /* '|' */)\n            return true;\n        if (next == 92 /* '\\\\' */)\n            i++;\n    }\n    return false;\n}\nconst delimiterLine = /^\\|?(\\s*:?-+:?\\s*\\|)+(\\s*:?-+:?\\s*)?$/;\nclass TableParser {\n    constructor() {\n        // Null means we haven't seen the second line yet, false means this\n        // isn't a table, and an array means this is a table and we've\n        // parsed the given rows so far.\n        this.rows = null;\n    }\n    nextLine(cx, line, leaf) {\n        if (this.rows == null) { // Second line\n            this.rows = false;\n            let lineText;\n            if ((line.next == 45 || line.next == 58 || line.next == 124 /* '-:|' */) &&\n                delimiterLine.test(lineText = line.text.slice(line.pos))) {\n                let firstRow = [], firstCount = parseRow(cx, leaf.content, 0, firstRow, leaf.start);\n                if (firstCount == parseRow(cx, lineText, line.pos))\n                    this.rows = [cx.elt(\"TableHeader\", leaf.start, leaf.start + leaf.content.length, firstRow),\n                        cx.elt(\"TableDelimiter\", cx.lineStart + line.pos, cx.lineStart + line.text.length)];\n            }\n        }\n        else if (this.rows) { // Line after the second\n            let content = [];\n            parseRow(cx, line.text, line.pos, content, cx.lineStart);\n            this.rows.push(cx.elt(\"TableRow\", cx.lineStart + line.pos, cx.lineStart + line.text.length, content));\n        }\n        return false;\n    }\n    finish(cx, leaf) {\n        if (!this.rows)\n            return false;\n        cx.addLeafElement(leaf, cx.elt(\"Table\", leaf.start, leaf.start + leaf.content.length, this.rows));\n        return true;\n    }\n}\n/**\nThis extension provides\n[GFM-style](https://github.github.com/gfm/#tables-extension-)\ntables, using syntax like this:\n\n```\n| head 1 | head 2 |\n| ---    | ---    |\n| cell 1 | cell 2 |\n```\n*/\nconst Table = {\n    defineNodes: [\n        { name: \"Table\", block: true },\n        { name: \"TableHeader\", style: { \"TableHeader/...\": tags.heading } },\n        \"TableRow\",\n        { name: \"TableCell\", style: tags.content },\n        { name: \"TableDelimiter\", style: tags.processingInstruction },\n    ],\n    parseBlock: [{\n            name: \"Table\",\n            leaf(_, leaf) { return hasPipe(leaf.content, 0) ? new TableParser : null; },\n            endLeaf(cx, line, leaf) {\n                if (leaf.parsers.some(p => p instanceof TableParser) || !hasPipe(line.text, line.basePos))\n                    return false;\n                let next = cx.peekLine();\n                return delimiterLine.test(next) && parseRow(cx, line.text, line.basePos) == parseRow(cx, next, line.basePos);\n            },\n            before: \"SetextHeading\"\n        }]\n};\nclass TaskParser {\n    nextLine() { return false; }\n    finish(cx, leaf) {\n        cx.addLeafElement(leaf, cx.elt(\"Task\", leaf.start, leaf.start + leaf.content.length, [\n            cx.elt(\"TaskMarker\", leaf.start, leaf.start + 3),\n            ...cx.parser.parseInline(leaf.content.slice(3), leaf.start + 3)\n        ]));\n        return true;\n    }\n}\n/**\nExtension providing\n[GFM-style](https://github.github.com/gfm/#task-list-items-extension-)\ntask list items, where list items can be prefixed with `[ ]` or\n`[x]` to add a checkbox.\n*/\nconst TaskList = {\n    defineNodes: [\n        { name: \"Task\", block: true, style: tags.list },\n        { name: \"TaskMarker\", style: tags.atom }\n    ],\n    parseBlock: [{\n            name: \"TaskList\",\n            leaf(cx, leaf) {\n                return /^\\[[ xX]\\][ \\t]/.test(leaf.content) && cx.parentType().name == \"ListItem\" ? new TaskParser : null;\n            },\n            after: \"SetextHeading\"\n        }]\n};\nconst autolinkRE = /(www\\.)|(https?:\\/\\/)|([\\w.+-]{1,100}@)|(mailto:|xmpp:)/gy;\nconst urlRE = /[\\w-]+(\\.[\\w-]+)+(\\/[^\\s<]*)?/gy;\nconst lastTwoDomainWords = /[\\w-]+\\.[\\w-]+($|\\/)/;\nconst emailRE = /[\\w.+-]+@[\\w-]+(\\.[\\w.-]+)+/gy;\nconst xmppResourceRE = /\\/[a-zA-Z\\d@.]+/gy;\nfunction count(str, from, to, ch) {\n    let result = 0;\n    for (let i = from; i < to; i++)\n        if (str[i] == ch)\n            result++;\n    return result;\n}\nfunction autolinkURLEnd(text, from) {\n    urlRE.lastIndex = from;\n    let m = urlRE.exec(text);\n    if (!m || lastTwoDomainWords.exec(m[0])[0].indexOf(\"_\") > -1)\n        return -1;\n    let end = from + m[0].length;\n    for (;;) {\n        let last = text[end - 1], m;\n        if (/[?!.,:*_~]/.test(last) ||\n            last == \")\" && count(text, from, end, \")\") > count(text, from, end, \"(\"))\n            end--;\n        else if (last == \";\" && (m = /&(?:#\\d+|#x[a-f\\d]+|\\w+);$/.exec(text.slice(from, end))))\n            end = from + m.index;\n        else\n            break;\n    }\n    return end;\n}\nfunction autolinkEmailEnd(text, from) {\n    emailRE.lastIndex = from;\n    let m = emailRE.exec(text);\n    if (!m)\n        return -1;\n    let last = m[0][m[0].length - 1];\n    return last == \"_\" || last == \"-\" ? -1 : from + m[0].length - (last == \".\" ? 1 : 0);\n}\n/**\nExtension that implements autolinking for\n`www.`/`http://`/`https://`/`mailto:`/`xmpp:` URLs and email\naddresses.\n*/\nconst Autolink = {\n    parseInline: [{\n            name: \"Autolink\",\n            parse(cx, next, absPos) {\n                let pos = absPos - cx.offset;\n                if (pos && /\\w/.test(cx.text[pos - 1]))\n                    return -1;\n                autolinkRE.lastIndex = pos;\n                let m = autolinkRE.exec(cx.text), end = -1;\n                if (!m)\n                    return -1;\n                if (m[1] || m[2]) { // www., http://\n                    end = autolinkURLEnd(cx.text, pos + m[0].length);\n                    if (end > -1 && cx.hasOpenLink) {\n                        let noBracket = /([^\\[\\]]|\\[[^\\]]*\\])*/.exec(cx.text.slice(pos, end));\n                        end = pos + noBracket[0].length;\n                    }\n                }\n                else if (m[3]) { // email address\n                    end = autolinkEmailEnd(cx.text, pos);\n                }\n                else { // mailto:/xmpp:\n                    end = autolinkEmailEnd(cx.text, pos + m[0].length);\n                    if (end > -1 && m[0] == \"xmpp:\") {\n                        xmppResourceRE.lastIndex = end;\n                        m = xmppResourceRE.exec(cx.text);\n                        if (m)\n                            end = m.index + m[0].length;\n                    }\n                }\n                if (end < 0)\n                    return -1;\n                cx.addElement(cx.elt(\"URL\", absPos, end + cx.offset));\n                return end + cx.offset;\n            }\n        }]\n};\n/**\nExtension bundle containing [`Table`](#Table),\n[`TaskList`](#TaskList), [`Strikethrough`](#Strikethrough), and\n[`Autolink`](#Autolink).\n*/\nconst GFM = [Table, TaskList, Strikethrough, Autolink];\nfunction parseSubSuper(ch, node, mark) {\n    return (cx, next, pos) => {\n        if (next != ch || cx.char(pos + 1) == ch)\n            return -1;\n        let elts = [cx.elt(mark, pos, pos + 1)];\n        for (let i = pos + 1; i < cx.end; i++) {\n            let next = cx.char(i);\n            if (next == ch)\n                return cx.addElement(cx.elt(node, pos, i + 1, elts.concat(cx.elt(mark, i, i + 1))));\n            if (next == 92 /* '\\\\' */)\n                elts.push(cx.elt(\"Escape\", i, i++ + 2));\n            if (space(next))\n                break;\n        }\n        return -1;\n    };\n}\n/**\nExtension providing\n[Pandoc-style](https://pandoc.org/MANUAL.html#superscripts-and-subscripts)\nsuperscript using `^` markers.\n*/\nconst Superscript = {\n    defineNodes: [\n        { name: \"Superscript\", style: tags.special(tags.content) },\n        { name: \"SuperscriptMark\", style: tags.processingInstruction }\n    ],\n    parseInline: [{\n            name: \"Superscript\",\n            parse: parseSubSuper(94 /* '^' */, \"Superscript\", \"SuperscriptMark\")\n        }]\n};\n/**\nExtension providing\n[Pandoc-style](https://pandoc.org/MANUAL.html#superscripts-and-subscripts)\nsubscript using `~` markers.\n*/\nconst Subscript = {\n    defineNodes: [\n        { name: \"Subscript\", style: tags.special(tags.content) },\n        { name: \"SubscriptMark\", style: tags.processingInstruction }\n    ],\n    parseInline: [{\n            name: \"Subscript\",\n            parse: parseSubSuper(126 /* '~' */, \"Subscript\", \"SubscriptMark\")\n        }]\n};\n/**\nExtension that parses two colons with only letters, underscores,\nand numbers between them as `Emoji` nodes.\n*/\nconst Emoji = {\n    defineNodes: [{ name: \"Emoji\", style: tags.character }],\n    parseInline: [{\n            name: \"Emoji\",\n            parse(cx, next, pos) {\n                let match;\n                if (next != 58 /* ':' */ || !(match = /^[a-zA-Z_0-9]+:/.exec(cx.slice(pos + 1, cx.end))))\n                    return -1;\n                return cx.addElement(cx.elt(\"Emoji\", pos, pos + 1 + match[0].length));\n            }\n        }]\n};\n\nexport { Autolink, BlockContext, Element, Emoji, GFM, InlineContext, LeafBlock, Line, MarkdownParser, Strikethrough, Subscript, Superscript, Table, TaskList, parseCode, parser };\n", "import { EditorSelection, countColumn, Prec, EditorState } from '@codemirror/state';\nimport { keymap } from '@codemirror/view';\nimport { defineLanguageFacet, foldNodeProp, indentNodeProp, languageDataProp, foldService, syntaxTree, Language, LanguageDescription, ParseContext, indentUnit, LanguageSupport } from '@codemirror/language';\nimport { CompletionContext } from '@codemirror/autocomplete';\nimport { parser, GFM, Subscript, Superscript, Emoji, MarkdownParser, parseCode } from '@lezer/markdown';\nimport { html, htmlCompletionSource } from '@codemirror/lang-html';\nimport { NodeProp } from '@lezer/common';\n\nconst data = /*@__PURE__*/defineLanguageFacet({ commentTokens: { block: { open: \"<!--\", close: \"-->\" } } });\nconst headingProp = /*@__PURE__*/new NodeProp();\nconst commonmark = /*@__PURE__*/parser.configure({\n    props: [\n        /*@__PURE__*/foldNodeProp.add(type => {\n            return !type.is(\"Block\") || type.is(\"Document\") || isHeading(type) != null || isList(type) ? undefined\n                : (tree, state) => ({ from: state.doc.lineAt(tree.from).to, to: tree.to });\n        }),\n        /*@__PURE__*/headingProp.add(isHeading),\n        /*@__PURE__*/indentNodeProp.add({\n            Document: () => null\n        }),\n        /*@__PURE__*/languageDataProp.add({\n            Document: data\n        })\n    ]\n});\nfunction isHeading(type) {\n    let match = /^(?:ATX|Setext)Heading(\\d)$/.exec(type.name);\n    return match ? +match[1] : undefined;\n}\nfunction isList(type) {\n    return type.name == \"OrderedList\" || type.name == \"BulletList\";\n}\nfunction findSectionEnd(headerNode, level) {\n    let last = headerNode;\n    for (;;) {\n        let next = last.nextSibling, heading;\n        if (!next || (heading = isHeading(next.type)) != null && heading <= level)\n            break;\n        last = next;\n    }\n    return last.to;\n}\nconst headerIndent = /*@__PURE__*/foldService.of((state, start, end) => {\n    for (let node = syntaxTree(state).resolveInner(end, -1); node; node = node.parent) {\n        if (node.from < start)\n            break;\n        let heading = node.type.prop(headingProp);\n        if (heading == null)\n            continue;\n        let upto = findSectionEnd(node, heading);\n        if (upto > end)\n            return { from: end, to: upto };\n    }\n    return null;\n});\nfunction mkLang(parser) {\n    return new Language(data, parser, [headerIndent], \"markdown\");\n}\n/**\nLanguage support for strict CommonMark.\n*/\nconst commonmarkLanguage = /*@__PURE__*/mkLang(commonmark);\nconst extended = /*@__PURE__*/commonmark.configure([GFM, Subscript, Superscript, Emoji, {\n        props: [\n            /*@__PURE__*/foldNodeProp.add({\n                Table: (tree, state) => ({ from: state.doc.lineAt(tree.from).to, to: tree.to })\n            })\n        ]\n    }]);\n/**\nLanguage support for [GFM](https://github.github.com/gfm/) plus\nsubscript, superscript, and emoji syntax.\n*/\nconst markdownLanguage = /*@__PURE__*/mkLang(extended);\nfunction getCodeParser(languages, defaultLanguage) {\n    return (info) => {\n        if (info && languages) {\n            let found = null;\n            // Strip anything after whitespace\n            info = /\\S*/.exec(info)[0];\n            if (typeof languages == \"function\")\n                found = languages(info);\n            else\n                found = LanguageDescription.matchLanguageName(languages, info, true);\n            if (found instanceof LanguageDescription)\n                return found.support ? found.support.language.parser : ParseContext.getSkippingParser(found.load());\n            else if (found)\n                return found.parser;\n        }\n        return defaultLanguage ? defaultLanguage.parser : null;\n    };\n}\n\nclass Context {\n    constructor(node, from, to, spaceBefore, spaceAfter, type, item) {\n        this.node = node;\n        this.from = from;\n        this.to = to;\n        this.spaceBefore = spaceBefore;\n        this.spaceAfter = spaceAfter;\n        this.type = type;\n        this.item = item;\n    }\n    blank(maxWidth, trailing = true) {\n        let result = this.spaceBefore + (this.node.name == \"Blockquote\" ? \">\" : \"\");\n        if (maxWidth != null) {\n            while (result.length < maxWidth)\n                result += \" \";\n            return result;\n        }\n        else {\n            for (let i = this.to - this.from - result.length - this.spaceAfter.length; i > 0; i--)\n                result += \" \";\n            return result + (trailing ? this.spaceAfter : \"\");\n        }\n    }\n    marker(doc, add) {\n        let number = this.node.name == \"OrderedList\" ? String((+itemNumber(this.item, doc)[2] + add)) : \"\";\n        return this.spaceBefore + number + this.type + this.spaceAfter;\n    }\n}\nfunction getContext(node, doc) {\n    let nodes = [], context = [];\n    for (let cur = node; cur; cur = cur.parent) {\n        if (cur.name == \"FencedCode\")\n            return context;\n        if (cur.name == \"ListItem\" || cur.name == \"Blockquote\")\n            nodes.push(cur);\n    }\n    for (let i = nodes.length - 1; i >= 0; i--) {\n        let node = nodes[i], match;\n        let line = doc.lineAt(node.from), startPos = node.from - line.from;\n        if (node.name == \"Blockquote\" && (match = /^ *>( ?)/.exec(line.text.slice(startPos)))) {\n            context.push(new Context(node, startPos, startPos + match[0].length, \"\", match[1], \">\", null));\n        }\n        else if (node.name == \"ListItem\" && node.parent.name == \"OrderedList\" &&\n            (match = /^( *)\\d+([.)])( *)/.exec(line.text.slice(startPos)))) {\n            let after = match[3], len = match[0].length;\n            if (after.length >= 4) {\n                after = after.slice(0, after.length - 4);\n                len -= 4;\n            }\n            context.push(new Context(node.parent, startPos, startPos + len, match[1], after, match[2], node));\n        }\n        else if (node.name == \"ListItem\" && node.parent.name == \"BulletList\" &&\n            (match = /^( *)([-+*])( {1,4}\\[[ xX]\\])?( +)/.exec(line.text.slice(startPos)))) {\n            let after = match[4], len = match[0].length;\n            if (after.length > 4) {\n                after = after.slice(0, after.length - 4);\n                len -= 4;\n            }\n            let type = match[2];\n            if (match[3])\n                type += match[3].replace(/[xX]/, ' ');\n            context.push(new Context(node.parent, startPos, startPos + len, match[1], after, type, node));\n        }\n    }\n    return context;\n}\nfunction itemNumber(item, doc) {\n    return /^(\\s*)(\\d+)(?=[.)])/.exec(doc.sliceString(item.from, item.from + 10));\n}\nfunction renumberList(after, doc, changes, offset = 0) {\n    for (let prev = -1, node = after;;) {\n        if (node.name == \"ListItem\") {\n            let m = itemNumber(node, doc);\n            let number = +m[2];\n            if (prev >= 0) {\n                if (number != prev + 1)\n                    return;\n                changes.push({ from: node.from + m[1].length, to: node.from + m[0].length, insert: String(prev + 2 + offset) });\n            }\n            prev = number;\n        }\n        let next = node.nextSibling;\n        if (!next)\n            break;\n        node = next;\n    }\n}\nfunction normalizeIndent(content, state) {\n    let blank = /^[ \\t]*/.exec(content)[0].length;\n    if (!blank || state.facet(indentUnit) != \"\\t\")\n        return content;\n    let col = countColumn(content, 4, blank);\n    let space = \"\";\n    for (let i = col; i > 0;) {\n        if (i >= 4) {\n            space += \"\\t\";\n            i -= 4;\n        }\n        else {\n            space += \" \";\n            i--;\n        }\n    }\n    return space + content.slice(blank);\n}\n/**\nThis command, when invoked in Markdown context with cursor\nselection(s), will create a new line with the markup for\nblockquotes and lists that were active on the old line. If the\ncursor was directly after the end of the markup for the old line,\ntrailing whitespace and list markers are removed from that line.\n\nThe command does nothing in non-Markdown context, so it should\nnot be used as the only binding for Enter (even in a Markdown\ndocument, HTML and code regions might use a different language).\n*/\nconst insertNewlineContinueMarkup = ({ state, dispatch }) => {\n    let tree = syntaxTree(state), { doc } = state;\n    let dont = null, changes = state.changeByRange(range => {\n        if (!range.empty || !markdownLanguage.isActiveAt(state, range.from, -1) && !markdownLanguage.isActiveAt(state, range.from, 1))\n            return dont = { range };\n        let pos = range.from, line = doc.lineAt(pos);\n        let context = getContext(tree.resolveInner(pos, -1), doc);\n        while (context.length && context[context.length - 1].from > pos - line.from)\n            context.pop();\n        if (!context.length)\n            return dont = { range };\n        let inner = context[context.length - 1];\n        if (inner.to - inner.spaceAfter.length > pos - line.from)\n            return dont = { range };\n        let emptyLine = pos >= (inner.to - inner.spaceAfter.length) && !/\\S/.test(line.text.slice(inner.to));\n        // Empty line in list\n        if (inner.item && emptyLine) {\n            let first = inner.node.firstChild, second = inner.node.getChild(\"ListItem\", \"ListItem\");\n            // Not second item or blank line before: delete a level of markup\n            if (first.to >= pos || second && second.to < pos ||\n                line.from > 0 && !/[^\\s>]/.test(doc.lineAt(line.from - 1).text)) {\n                let next = context.length > 1 ? context[context.length - 2] : null;\n                let delTo, insert = \"\";\n                if (next && next.item) { // Re-add marker for the list at the next level\n                    delTo = line.from + next.from;\n                    insert = next.marker(doc, 1);\n                }\n                else {\n                    delTo = line.from + (next ? next.to : 0);\n                }\n                let changes = [{ from: delTo, to: pos, insert }];\n                if (inner.node.name == \"OrderedList\")\n                    renumberList(inner.item, doc, changes, -2);\n                if (next && next.node.name == \"OrderedList\")\n                    renumberList(next.item, doc, changes);\n                return { range: EditorSelection.cursor(delTo + insert.length), changes };\n            }\n            else { // Move second item down, making tight two-item list non-tight\n                let insert = blankLine(context, state, line);\n                return { range: EditorSelection.cursor(pos + insert.length + 1),\n                    changes: { from: line.from, insert: insert + state.lineBreak } };\n            }\n        }\n        if (inner.node.name == \"Blockquote\" && emptyLine && line.from) {\n            let prevLine = doc.lineAt(line.from - 1), quoted = />\\s*$/.exec(prevLine.text);\n            // Two aligned empty quoted lines in a row\n            if (quoted && quoted.index == inner.from) {\n                let changes = state.changes([{ from: prevLine.from + quoted.index, to: prevLine.to },\n                    { from: line.from + inner.from, to: line.to }]);\n                return { range: range.map(changes), changes };\n            }\n        }\n        let changes = [];\n        if (inner.node.name == \"OrderedList\")\n            renumberList(inner.item, doc, changes);\n        let continued = inner.item && inner.item.from < line.from;\n        let insert = \"\";\n        // If not dedented\n        if (!continued || /^[\\s\\d.)\\-+*>]*/.exec(line.text)[0].length >= inner.to) {\n            for (let i = 0, e = context.length - 1; i <= e; i++) {\n                insert += i == e && !continued ? context[i].marker(doc, 1)\n                    : context[i].blank(i < e ? countColumn(line.text, 4, context[i + 1].from) - insert.length : null);\n            }\n        }\n        let from = pos;\n        while (from > line.from && /\\s/.test(line.text.charAt(from - line.from - 1)))\n            from--;\n        insert = normalizeIndent(insert, state);\n        if (nonTightList(inner.node, state.doc))\n            insert = blankLine(context, state, line) + state.lineBreak + insert;\n        changes.push({ from, to: pos, insert: state.lineBreak + insert });\n        return { range: EditorSelection.cursor(from + insert.length + 1), changes };\n    });\n    if (dont)\n        return false;\n    dispatch(state.update(changes, { scrollIntoView: true, userEvent: \"input\" }));\n    return true;\n};\nfunction isMark(node) {\n    return node.name == \"QuoteMark\" || node.name == \"ListMark\";\n}\nfunction nonTightList(node, doc) {\n    if (node.name != \"OrderedList\" && node.name != \"BulletList\")\n        return false;\n    let first = node.firstChild, second = node.getChild(\"ListItem\", \"ListItem\");\n    if (!second)\n        return false;\n    let line1 = doc.lineAt(first.to), line2 = doc.lineAt(second.from);\n    let empty = /^[\\s>]*$/.test(line1.text);\n    return line1.number + (empty ? 0 : 1) < line2.number;\n}\nfunction blankLine(context, state, line) {\n    let insert = \"\";\n    for (let i = 0, e = context.length - 2; i <= e; i++) {\n        insert += context[i].blank(i < e\n            ? countColumn(line.text, 4, context[i + 1].from) - insert.length\n            : null, i < e);\n    }\n    return normalizeIndent(insert, state);\n}\nfunction contextNodeForDelete(tree, pos) {\n    let node = tree.resolveInner(pos, -1), scan = pos;\n    if (isMark(node)) {\n        scan = node.from;\n        node = node.parent;\n    }\n    for (let prev; prev = node.childBefore(scan);) {\n        if (isMark(prev)) {\n            scan = prev.from;\n        }\n        else if (prev.name == \"OrderedList\" || prev.name == \"BulletList\") {\n            node = prev.lastChild;\n            scan = node.to;\n        }\n        else {\n            break;\n        }\n    }\n    return node;\n}\n/**\nThis command will, when invoked in a Markdown context with the\ncursor directly after list or blockquote markup, delete one level\nof markup. When the markup is for a list, it will be replaced by\nspaces on the first invocation (a further invocation will delete\nthe spaces), to make it easy to continue a list.\n\nWhen not after Markdown block markup, this command will return\nfalse, so it is intended to be bound alongside other deletion\ncommands, with a higher precedence than the more generic commands.\n*/\nconst deleteMarkupBackward = ({ state, dispatch }) => {\n    let tree = syntaxTree(state);\n    let dont = null, changes = state.changeByRange(range => {\n        let pos = range.from, { doc } = state;\n        if (range.empty && markdownLanguage.isActiveAt(state, range.from)) {\n            let line = doc.lineAt(pos);\n            let context = getContext(contextNodeForDelete(tree, pos), doc);\n            if (context.length) {\n                let inner = context[context.length - 1];\n                let spaceEnd = inner.to - inner.spaceAfter.length + (inner.spaceAfter ? 1 : 0);\n                // Delete extra trailing space after markup\n                if (pos - line.from > spaceEnd && !/\\S/.test(line.text.slice(spaceEnd, pos - line.from)))\n                    return { range: EditorSelection.cursor(line.from + spaceEnd),\n                        changes: { from: line.from + spaceEnd, to: pos } };\n                if (pos - line.from == spaceEnd &&\n                    // Only apply this if we're on the line that has the\n                    // construct's syntax, or there's only indentation in the\n                    // target range\n                    (!inner.item || line.from <= inner.item.from || !/\\S/.test(line.text.slice(0, inner.to)))) {\n                    let start = line.from + inner.from;\n                    // Replace a list item marker with blank space\n                    if (inner.item && inner.node.from < inner.item.from && /\\S/.test(line.text.slice(inner.from, inner.to))) {\n                        let insert = inner.blank(countColumn(line.text, 4, inner.to) - countColumn(line.text, 4, inner.from));\n                        if (start == line.from)\n                            insert = normalizeIndent(insert, state);\n                        return { range: EditorSelection.cursor(start + insert.length),\n                            changes: { from: start, to: line.from + inner.to, insert } };\n                    }\n                    // Delete one level of indentation\n                    if (start < pos)\n                        return { range: EditorSelection.cursor(start), changes: { from: start, to: pos } };\n                }\n            }\n        }\n        return dont = { range };\n    });\n    if (dont)\n        return false;\n    dispatch(state.update(changes, { scrollIntoView: true, userEvent: \"delete\" }));\n    return true;\n};\n\n/**\nA small keymap with Markdown-specific bindings. Binds Enter to\n[`insertNewlineContinueMarkup`](https://codemirror.net/6/docs/ref/#lang-markdown.insertNewlineContinueMarkup)\nand Backspace to\n[`deleteMarkupBackward`](https://codemirror.net/6/docs/ref/#lang-markdown.deleteMarkupBackward).\n*/\nconst markdownKeymap = [\n    { key: \"Enter\", run: insertNewlineContinueMarkup },\n    { key: \"Backspace\", run: deleteMarkupBackward }\n];\nconst htmlNoMatch = /*@__PURE__*/html({ matchClosingTags: false });\n/**\nMarkdown language support.\n*/\nfunction markdown(config = {}) {\n    let { codeLanguages, defaultCodeLanguage, addKeymap = true, base: { parser } = commonmarkLanguage, completeHTMLTags = true, htmlTagLanguage = htmlNoMatch } = config;\n    if (!(parser instanceof MarkdownParser))\n        throw new RangeError(\"Base parser provided to `markdown` should be a Markdown parser\");\n    let extensions = config.extensions ? [config.extensions] : [];\n    let support = [htmlTagLanguage.support], defaultCode;\n    if (defaultCodeLanguage instanceof LanguageSupport) {\n        support.push(defaultCodeLanguage.support);\n        defaultCode = defaultCodeLanguage.language;\n    }\n    else if (defaultCodeLanguage) {\n        defaultCode = defaultCodeLanguage;\n    }\n    let codeParser = codeLanguages || defaultCode ? getCodeParser(codeLanguages, defaultCode) : undefined;\n    extensions.push(parseCode({ codeParser, htmlParser: htmlTagLanguage.language.parser }));\n    if (addKeymap)\n        support.push(Prec.high(keymap.of(markdownKeymap)));\n    let lang = mkLang(parser.configure(extensions));\n    if (completeHTMLTags)\n        support.push(lang.data.of({ autocomplete: htmlTagCompletion }));\n    return new LanguageSupport(lang, support);\n}\nfunction htmlTagCompletion(context) {\n    let { state, pos } = context, m = /<[:\\-\\.\\w\\u00b7-\\uffff]*$/.exec(state.sliceDoc(pos - 25, pos));\n    if (!m)\n        return null;\n    let tree = syntaxTree(state).resolveInner(pos, -1);\n    while (tree && !tree.type.isTop) {\n        if (tree.name == \"CodeBlock\" || tree.name == \"FencedCode\" || tree.name == \"ProcessingInstructionBlock\" ||\n            tree.name == \"CommentBlock\" || tree.name == \"Link\" || tree.name == \"Image\")\n            return null;\n        tree = tree.parent;\n    }\n    return {\n        from: pos - m[0].length, to: pos,\n        options: htmlTagCompletions(),\n        validFor: /^<[:\\-\\.\\w\\u00b7-\\uffff]*$/\n    };\n}\nlet _tagCompletions = null;\nfunction htmlTagCompletions() {\n    if (_tagCompletions)\n        return _tagCompletions;\n    let result = htmlCompletionSource(new CompletionContext(EditorState.create({ extensions: htmlNoMatch }), 0, true));\n    return _tagCompletions = result ? result.options : [];\n}\n\nexport { commonmarkLanguage, deleteMarkupBackward, insertNewlineContinueMarkup, markdown, markdownKeymap, markdownLanguage };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAGA,IAAM,iBAAN,MAAM,gBAAe;AAAA,EACjB,OAAO,OAAO,MAAM,OAAO,MAAM,YAAY,KAAK;AAC9C,QAAI,OAAQ,cAAc,cAAc,KAAK,QAAQ,SAAS,KAAM;AACpE,WAAO,IAAI,gBAAe,MAAM,OAAO,MAAM,MAAM,KAAK,CAAC,GAAG,CAAC,CAAC;AAAA,EAClE;AAAA,EACA,YAAY,MAEZ,OAAO,MAAM,MAAM,KAAK,UAAU,WAAW;AACzC,SAAK,OAAO;AACZ,SAAK,QAAQ;AACb,SAAK,OAAO;AACZ,SAAK,OAAO;AACZ,SAAK,MAAM;AACX,SAAK,WAAW;AAChB,SAAK,YAAY;AACjB,SAAK,WAAW,CAAC,CAAC,SAAS,aAAa,IAAI,CAAC;AAAA,EACjD;AAAA,EACA,SAAS,OAAO,KAAK;AACjB,QAAI,MAAM,KAAK,SAAS,WAAW,KAAK,KAAK;AACzC,cAAQ,IAAI,KAAK,MAAM,MAAM,MAAM,UAAU,MAAM,WAAW,MAAM,QAAQ,KAAK,QAAQ;AAC7F,SAAK,SAAS,KAAK,KAAK;AACxB,SAAK,UAAU,KAAK,GAAG;AAAA,EAC3B;AAAA,EACA,OAAO,SAAS,MAAM,KAAK,KAAK;AAC5B,QAAI,OAAO,KAAK,SAAS,SAAS;AAClC,QAAI,QAAQ;AACR,YAAM,KAAK,IAAI,KAAK,KAAK,UAAU,IAAI,IAAI,KAAK,SAAS,IAAI,EAAE,SAAS,KAAK,IAAI;AACrF,WAAO,IAAI,KAAK,QAAQ,MAAM,KAAK,IAAI,GAAG,KAAK,UAAU,KAAK,WAAW,MAAM,KAAK,IAAI,EAAE,QAAQ;AAAA,MAC9F,UAAU,CAAC,UAAU,WAAW,WAAW,IAAI,KAAK,SAAS,MAAM,UAAU,WAAW,QAAQ,KAAK,QAAQ;AAAA,IACjH,CAAC;AAAA,EACL;AACJ;AACA,IAAI;AAAA,CACH,SAAUA,OAAM;AACb,EAAAA,MAAKA,MAAK,UAAU,IAAI,CAAC,IAAI;AAC7B,EAAAA,MAAKA,MAAK,WAAW,IAAI,CAAC,IAAI;AAC9B,EAAAA,MAAKA,MAAK,YAAY,IAAI,CAAC,IAAI;AAC/B,EAAAA,MAAKA,MAAK,YAAY,IAAI,CAAC,IAAI;AAC/B,EAAAA,MAAKA,MAAK,gBAAgB,IAAI,CAAC,IAAI;AACnC,EAAAA,MAAKA,MAAK,YAAY,IAAI,CAAC,IAAI;AAC/B,EAAAA,MAAKA,MAAK,aAAa,IAAI,CAAC,IAAI;AAChC,EAAAA,MAAKA,MAAK,UAAU,IAAI,CAAC,IAAI;AAC7B,EAAAA,MAAKA,MAAK,aAAa,IAAI,CAAC,IAAI;AAChC,EAAAA,MAAKA,MAAK,aAAa,IAAI,EAAE,IAAI;AACjC,EAAAA,MAAKA,MAAK,aAAa,IAAI,EAAE,IAAI;AACjC,EAAAA,MAAKA,MAAK,aAAa,IAAI,EAAE,IAAI;AACjC,EAAAA,MAAKA,MAAK,aAAa,IAAI,EAAE,IAAI;AACjC,EAAAA,MAAKA,MAAK,aAAa,IAAI,EAAE,IAAI;AACjC,EAAAA,MAAKA,MAAK,gBAAgB,IAAI,EAAE,IAAI;AACpC,EAAAA,MAAKA,MAAK,gBAAgB,IAAI,EAAE,IAAI;AACpC,EAAAA,MAAKA,MAAK,WAAW,IAAI,EAAE,IAAI;AAC/B,EAAAA,MAAKA,MAAK,eAAe,IAAI,EAAE,IAAI;AACnC,EAAAA,MAAKA,MAAK,WAAW,IAAI,EAAE,IAAI;AAC/B,EAAAA,MAAKA,MAAK,cAAc,IAAI,EAAE,IAAI;AAClC,EAAAA,MAAKA,MAAK,4BAA4B,IAAI,EAAE,IAAI;AAEhD,EAAAA,MAAKA,MAAK,QAAQ,IAAI,EAAE,IAAI;AAC5B,EAAAA,MAAKA,MAAK,QAAQ,IAAI,EAAE,IAAI;AAC5B,EAAAA,MAAKA,MAAK,WAAW,IAAI,EAAE,IAAI;AAC/B,EAAAA,MAAKA,MAAK,UAAU,IAAI,EAAE,IAAI;AAC9B,EAAAA,MAAKA,MAAK,gBAAgB,IAAI,EAAE,IAAI;AACpC,EAAAA,MAAKA,MAAK,MAAM,IAAI,EAAE,IAAI;AAC1B,EAAAA,MAAKA,MAAK,OAAO,IAAI,EAAE,IAAI;AAC3B,EAAAA,MAAKA,MAAK,YAAY,IAAI,EAAE,IAAI;AAChC,EAAAA,MAAKA,MAAK,SAAS,IAAI,EAAE,IAAI;AAC7B,EAAAA,MAAKA,MAAK,SAAS,IAAI,EAAE,IAAI;AAC7B,EAAAA,MAAKA,MAAK,uBAAuB,IAAI,EAAE,IAAI;AAC3C,EAAAA,MAAKA,MAAK,UAAU,IAAI,EAAE,IAAI;AAE9B,EAAAA,MAAKA,MAAK,YAAY,IAAI,EAAE,IAAI;AAChC,EAAAA,MAAKA,MAAK,WAAW,IAAI,EAAE,IAAI;AAC/B,EAAAA,MAAKA,MAAK,UAAU,IAAI,EAAE,IAAI;AAC9B,EAAAA,MAAKA,MAAK,UAAU,IAAI,EAAE,IAAI;AAC9B,EAAAA,MAAKA,MAAK,cAAc,IAAI,EAAE,IAAI;AAClC,EAAAA,MAAKA,MAAK,UAAU,IAAI,EAAE,IAAI;AAC9B,EAAAA,MAAKA,MAAK,UAAU,IAAI,EAAE,IAAI;AAC9B,EAAAA,MAAKA,MAAK,UAAU,IAAI,EAAE,IAAI;AAC9B,EAAAA,MAAKA,MAAK,WAAW,IAAI,EAAE,IAAI;AAC/B,EAAAA,MAAKA,MAAK,WAAW,IAAI,EAAE,IAAI;AAC/B,EAAAA,MAAKA,MAAK,KAAK,IAAI,EAAE,IAAI;AAC7B,GAAG,SAAS,OAAO,CAAC,EAAE;AAKtB,IAAM,YAAN,MAAgB;AAAA;AAAA;AAAA;AAAA,EAIZ,YAIA,OAIA,SAAS;AACL,SAAK,QAAQ;AACb,SAAK,UAAU;AAIf,SAAK,QAAQ,CAAC;AAId,SAAK,UAAU,CAAC;AAAA,EACpB;AACJ;AAIA,IAAM,OAAN,MAAW;AAAA,EACP,cAAc;AAIV,SAAK,OAAO;AAKZ,SAAK,aAAa;AAIlB,SAAK,UAAU;AAIf,SAAK,QAAQ;AAIb,SAAK,UAAU,CAAC;AAKhB,SAAK,MAAM;AAIX,SAAK,SAAS;AAId,SAAK,OAAO;AAAA,EAChB;AAAA;AAAA;AAAA;AAAA,EAIA,UAAU;AACN,QAAI,KAAK,UAAU,KAAK;AACpB,WAAK,aAAa;AAAA,EAC1B;AAAA;AAAA;AAAA;AAAA,EAIA,eAAe;AACX,QAAI,SAAS,KAAK,UAAU,KAAK,OAAO;AACxC,SAAK,SAAS,KAAK,YAAY,QAAQ,KAAK,KAAK,KAAK,MAAM;AAC5D,SAAK,MAAM;AACX,SAAK,OAAO,UAAU,KAAK,KAAK,SAAS,KAAK,KAAK,KAAK,WAAW,MAAM;AAAA,EAC7E;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,UAAU,MAAM;AAAE,WAAO,UAAU,KAAK,MAAM,IAAI;AAAA,EAAG;AAAA;AAAA;AAAA;AAAA,EAIrD,MAAM,MAAM;AACR,SAAK,OAAO;AACZ,SAAK,aAAa,KAAK,UAAU,KAAK,MAAM,KAAK,SAAS;AAC1D,SAAK,aAAa;AAClB,SAAK,QAAQ;AACb,WAAO,KAAK,QAAQ;AAChB,WAAK,QAAQ,IAAI;AAAA,EACzB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,SAAS,IAAI;AACT,SAAK,UAAU;AACf,SAAK,aAAa,KAAK,YAAY,IAAI,KAAK,KAAK,KAAK,MAAM;AAAA,EAChE;AAAA;AAAA;AAAA;AAAA,EAIA,eAAe,QAAQ;AACnB,SAAK,aAAa;AAClB,SAAK,UAAU,KAAK,WAAW,MAAM;AAAA,EACzC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,UAAUC,MAAK;AACX,SAAK,QAAQ,KAAKA,IAAG;AAAA,EACzB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,YAAY,IAAI,OAAO,GAAG,SAAS,GAAG;AAClC,aAAS,IAAI,MAAM,IAAI,IAAI;AACvB,gBAAU,KAAK,KAAK,WAAW,CAAC,KAAK,IAAI,IAAI,SAAS,IAAI;AAC9D,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA,EAIA,WAAW,MAAM;AACb,QAAI,IAAI;AACR,aAAS,SAAS,GAAG,IAAI,KAAK,KAAK,UAAU,SAAS,MAAM;AACxD,gBAAU,KAAK,KAAK,WAAW,CAAC,KAAK,IAAI,IAAI,SAAS,IAAI;AAC9D,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA,EAIA,QAAQ;AACJ,QAAI,CAAC,KAAK;AACN,aAAO,KAAK;AAChB,QAAI,SAAS;AACb,aAAS,IAAI,GAAG,IAAI,KAAK,SAAS;AAC9B,gBAAU;AACd,WAAO,SAAS,KAAK,KAAK,MAAM,KAAK,OAAO;AAAA,EAChD;AACJ;AACA,SAAS,YAAY,IAAI,IAAI,MAAM;AAC/B,MAAI,KAAK,OAAO,KAAK,KAAK,UACrB,MAAM,GAAG,SAAS,KAAK,UAAU,GAAG,MAAM,KAAK,QAAQ,CAAC,EAAE,QAAQ,KAAK;AACxE,WAAO;AACX,MAAI,KAAK,UAAU,KAAK,aAAa;AACjC,WAAO;AACX,MAAI,QAAQ,GAAG,QAAQ,KAAK,cAAc,gBAAgB,cAAc,MAAM,IAAI,KAAK;AACvF,SAAO,OAAO,MACT,GAAG,QAAQ,KAAK,cAAc,iBAAiB,MAAM,IAAI,KAAK,IAAI,MACnE,KAAK,KAAK,WAAW,KAAK,MAAM,OAAO,CAAC,KAAK,GAAG;AACxD;AACA,IAAM,oBAAoB;AAAA,EACtB,CAAC,KAAK,UAAU,EAAE,IAAI,IAAI,MAAM;AAC5B,QAAI,KAAK,QAAQ;AACb,aAAO;AACX,SAAK,QAAQ,KAAK,IAAI,KAAK,WAAW,GAAG,YAAY,KAAK,KAAK,GAAG,YAAY,KAAK,MAAM,CAAC,CAAC;AAC3F,SAAK,SAAS,KAAK,OAAO,MAAM,KAAK,KAAK,WAAW,KAAK,MAAM,CAAC,CAAC,IAAI,IAAI,EAAE;AAC5E,OAAG,MAAM,GAAG,YAAY,KAAK,KAAK;AAClC,WAAO;AAAA,EACX;AAAA,EACA,CAAC,KAAK,QAAQ,EAAE,IAAI,KAAK,MAAM;AAC3B,QAAI,KAAK,SAAS,KAAK,aAAa,GAAG,SAAS,KAAK,OAAO;AACxD,aAAO;AACX,SAAK,eAAe,KAAK,aAAa,GAAG,KAAK;AAC9C,WAAO;AAAA,EACX;AAAA,EACA,CAAC,KAAK,WAAW,GAAG;AAAA,EACpB,CAAC,KAAK,UAAU,GAAG;AAAA,EACnB,CAAC,KAAK,QAAQ,IAAI;AAAE,WAAO;AAAA,EAAM;AACrC;AACA,SAAS,MAAM,IAAI;AAAE,SAAO,MAAM,MAAM,MAAM,KAAK,MAAM,MAAM,MAAM;AAAI;AACzE,SAAS,UAAU,MAAM,IAAI,GAAG;AAC5B,SAAO,IAAI,KAAK,UAAU,MAAM,KAAK,WAAW,CAAC,CAAC;AAC9C;AACJ,SAAO;AACX;AACA,SAAS,cAAc,MAAM,GAAG,IAAI;AAChC,SAAO,IAAI,MAAM,MAAM,KAAK,WAAW,IAAI,CAAC,CAAC;AACzC;AACJ,SAAO;AACX;AACA,SAAS,aAAa,MAAM;AACxB,MAAI,KAAK,QAAQ,MAAM,KAAK,QAAQ;AAChC,WAAO;AACX,MAAI,MAAM,KAAK,MAAM;AACrB,SAAO,MAAM,KAAK,KAAK,UAAU,KAAK,KAAK,WAAW,GAAG,KAAK,KAAK;AAC/D;AACJ,MAAI,MAAM,KAAK,MAAM;AACjB,WAAO;AACX,MAAI,KAAK,QAAQ;AACb,aAAS,IAAI,KAAK,IAAI,KAAK,KAAK,QAAQ;AACpC,UAAI,KAAK,KAAK,WAAW,CAAC,KAAK;AAC3B,eAAO;AAAA;AACnB,SAAO;AACX;AACA,SAAS,aAAa,MAAM;AACxB,SAAO,KAAK,QAAQ,KAAe,KAAK,KAAK,KAAK,WAAW,KAAK,MAAM,CAAC,KAAK,KAAK,IAAI;AAC3F;AACA,SAAS,iBAAiB,MAAM,IAAI,UAAU;AAC1C,MAAI,KAAK,QAAQ,MAAM,KAAK,QAAQ,MAAM,KAAK,QAAQ;AACnD,WAAO;AACX,MAAIC,SAAQ;AACZ,WAAS,MAAM,KAAK,MAAM,GAAG,MAAM,KAAK,KAAK,QAAQ,OAAO;AACxD,QAAI,KAAK,KAAK,KAAK,WAAW,GAAG;AACjC,QAAI,MAAM,KAAK;AACX,MAAAA;AAAA,aACK,CAAC,MAAM,EAAE;AACd,aAAO;AAAA,EACf;AAEA,MAAI,YAAY,KAAK,QAAQ,MAAM,kBAAkB,IAAI,IAAI,MAAM,KAAK,SAAS,GAAG,MAAM,UACtF,GAAG,OAAO,iBAAiB,QAAQ,kBAAkB,aAAa,IAAI;AACtE,WAAO;AACX,SAAOA,SAAQ,IAAI,KAAK;AAC5B;AACA,SAAS,OAAO,IAAI,MAAM;AACtB,WAAS,IAAI,GAAG,MAAM,SAAS,GAAG,KAAK,GAAG;AACtC,QAAI,GAAG,MAAM,CAAC,EAAE,QAAQ;AACpB,aAAO;AACf,SAAO;AACX;AACA,SAAS,aAAa,MAAM,IAAI,UAAU;AACtC,UAAQ,KAAK,QAAQ,MAAM,KAAK,QAAQ,MAAM,KAAK,QAAQ,QACtD,KAAK,OAAO,KAAK,KAAK,SAAS,KAAK,MAAM,KAAK,KAAK,WAAW,KAAK,MAAM,CAAC,CAAC,OAC5E,CAAC,YAAY,OAAO,IAAI,KAAK,UAAU,KAAK,KAAK,UAAU,KAAK,MAAM,CAAC,IAAI,KAAK,KAAK,UAAU,IAAI;AAC5G;AACA,SAAS,cAAc,MAAM,IAAI,UAAU;AACvC,MAAI,MAAM,KAAK,KAAK,OAAO,KAAK;AAChC,aAAS;AACL,QAAI,QAAQ,MAAM,QAAQ;AACtB;AAAA;AAEA;AACJ,QAAI,OAAO,KAAK,KAAK;AACjB,aAAO;AACX,WAAO,KAAK,KAAK,WAAW,GAAG;AAAA,EACnC;AACA,MAAI,OAAO,KAAK,OAAO,MAAM,KAAK,MAAM,KACnC,QAAQ,MAAM,QAAQ,MACtB,MAAM,KAAK,KAAK,SAAS,KAAK,CAAC,MAAM,KAAK,KAAK,WAAW,MAAM,CAAC,CAAC,KACnE,YAAY,CAAC,OAAO,IAAI,KAAK,WAAW,MACnC,KAAK,UAAU,MAAM,CAAC,KAAK,KAAK,KAAK,UAAU,MAAM,KAAK,MAAM,KAAK,KAAK,QAAQ;AACvF,WAAO;AACX,SAAO,MAAM,IAAI,KAAK;AAC1B;AACA,SAAS,aAAa,MAAM;AACxB,MAAI,KAAK,QAAQ;AACb,WAAO;AACX,MAAI,MAAM,KAAK,MAAM;AACrB,SAAO,MAAM,KAAK,KAAK,UAAU,KAAK,KAAK,WAAW,GAAG,KAAK;AAC1D;AACJ,MAAI,MAAM,KAAK,KAAK,UAAU,KAAK,KAAK,WAAW,GAAG,KAAK;AACvD,WAAO;AACX,MAAI,OAAO,MAAM,KAAK;AACtB,SAAO,OAAO,IAAI,KAAK;AAC3B;AACA,SAAS,kBAAkB,MAAM;AAC7B,MAAI,KAAK,QAAQ,MAAM,KAAK,QAAQ,MAAiB,KAAK,UAAU,KAAK,aAAa;AAClF,WAAO;AACX,MAAI,MAAM,KAAK,MAAM;AACrB,SAAO,MAAM,KAAK,KAAK,UAAU,KAAK,KAAK,WAAW,GAAG,KAAK,KAAK;AAC/D;AACJ,MAAI,MAAM;AACV,SAAO,MAAM,KAAK,KAAK,UAAU,MAAM,KAAK,KAAK,WAAW,GAAG,CAAC;AAC5D;AACJ,SAAO,OAAO,KAAK,KAAK,SAAS,MAAM;AAC3C;AACA,IAAM,YAAY;AAAlB,IAA8B,aAAa;AAA3C,IAAkD,gBAAgB;AAClE,IAAM,iBAAiB;AAAA,EACnB,CAAC,qCAAqC,2BAA2B;AAAA,EACjE,CAAC,YAAY,UAAU;AAAA,EACvB,CAAC,WAAW,aAAa;AAAA,EACzB,CAAC,eAAe,GAAG;AAAA,EACnB,CAAC,mBAAmB,OAAO;AAAA,EAC3B,CAAC,iYAAiY,SAAS;AAAA,EAC3Y,CAAC,oHAAoH,SAAS;AAClI;AACA,SAAS,YAAY,MAAM,KAAK,UAAU;AACtC,MAAI,KAAK,QAAQ;AACb,WAAO;AACX,MAAI,OAAO,KAAK,KAAK,MAAM,KAAK,GAAG;AACnC,WAAS,IAAI,GAAG,IAAI,eAAe,UAAU,WAAW,IAAI,IAAI,IAAI,GAAG;AACnE,QAAI,eAAe,CAAC,EAAE,CAAC,EAAE,KAAK,IAAI;AAC9B,aAAO;AACf,SAAO;AACX;AACA,SAAS,cAAc,MAAM,KAAK;AAC9B,MAAI,cAAc,KAAK,YAAY,KAAK,KAAK,KAAK,KAAK,MAAM;AAC7D,MAAI,WAAW,KAAK,YAAY,KAAK,UAAU,GAAG,GAAG,KAAK,WAAW;AACrE,SAAO,YAAY,cAAc,IAAI,cAAc,IAAI;AAC3D;AACA,SAAS,YAAY,OAAO,MAAM,IAAI;AAClC,MAAI,OAAO,MAAM,SAAS;AAC1B,MAAI,QAAQ,KAAK,MAAM,IAAI,EAAE,MAAM,QAAQ,MAAM,IAAI,EAAE,QAAQ,KAAK;AAChE,UAAM,IAAI,EAAE,KAAK;AAAA;AAEjB,UAAM,KAAK,IAAI,KAAK,UAAU,MAAM,EAAE,CAAC;AAC/C;AAKA,IAAM,sBAAsB;AAAA,EACxB,eAAe;AAAA,EACf,aAAa,IAAI,MAAM;AACnB,QAAI,OAAO,KAAK,aAAa;AAC7B,QAAI,KAAK,SAAS;AACd,aAAO;AACX,QAAI,QAAQ,KAAK,WAAW,IAAI;AAChC,QAAI,OAAO,GAAG,YAAY,OAAO,KAAK,GAAG,YAAY,KAAK,KAAK;AAC/D,QAAI,QAAQ,CAAC,GAAG,eAAe,CAAC;AAChC,gBAAY,OAAO,MAAM,EAAE;AAC3B,WAAO,GAAG,SAAS,KAAK,KAAK,SAAS,GAAG,MAAM,QAAQ;AACnD,UAAI,KAAK,OAAO,KAAK,KAAK,QAAQ;AAC9B,oBAAY,cAAc,GAAG,YAAY,GAAG,GAAG,SAAS;AACxD,iBAAS,KAAK,KAAK;AACf,uBAAa,KAAK,CAAC;AAAA,MAC3B,WACS,KAAK,SAAS,MAAM;AACzB;AAAA,MACJ,OACK;AACD,YAAI,aAAa,QAAQ;AACrB,mBAAS,KAAK,cAAc;AACxB,gBAAI,EAAE,QAAQ,KAAK;AACf,0BAAY,OAAO,EAAE,MAAM,EAAE,EAAE;AAAA;AAE/B,oBAAM,KAAK,CAAC;AAAA,UACpB;AACA,yBAAe,CAAC;AAAA,QACpB;AACA,oBAAY,OAAO,GAAG,YAAY,GAAG,GAAG,SAAS;AACjD,iBAAS,KAAK,KAAK;AACf,gBAAM,KAAK,CAAC;AAChB,aAAK,GAAG,YAAY,KAAK,KAAK;AAC9B,YAAI,YAAY,GAAG,YAAY,KAAK,WAAW,KAAK,aAAa,CAAC;AAClE,YAAI,YAAY;AACZ,sBAAY,OAAO,WAAW,EAAE;AAAA,MACxC;AAAA,IACJ;AACA,QAAI,aAAa,QAAQ;AACrB,qBAAe,aAAa,OAAO,OAAK,EAAE,QAAQ,KAAK,QAAQ;AAC/D,UAAI,aAAa;AACb,aAAK,UAAU,aAAa,OAAO,KAAK,OAAO;AAAA,IACvD;AACA,OAAG,QAAQ,GAAG,OAAO,cAAc,OAAO,CAAC,IAAI,EAAE,OAAO,KAAK,WAAW,KAAK,IAAI,GAAG,IAAI;AACxF,WAAO;AAAA,EACX;AAAA,EACA,WAAW,IAAI,MAAM;AACjB,QAAI,WAAW,aAAa,IAAI;AAChC,QAAI,WAAW;AACX,aAAO;AACX,QAAI,OAAO,GAAG,YAAY,KAAK,KAAK,KAAK,KAAK,MAAM,MAAM,WAAW,KAAK;AAC1E,QAAI,WAAW,KAAK,UAAU,QAAQ,GAAG,SAAS,cAAc,KAAK,MAAM,KAAK,KAAK,QAAQ,QAAQ;AACrG,QAAI,QAAQ,CAAC,IAAI,KAAK,UAAU,MAAM,OAAO,GAAG,CAAC;AACjD,QAAI,WAAW;AACX,YAAM,KAAK,IAAI,KAAK,UAAU,GAAG,YAAY,UAAU,GAAG,YAAY,MAAM,CAAC;AACjF,aAAS,QAAQ,MAAM,GAAG,SAAS,KAAK,KAAK,SAAS,GAAG,MAAM,QAAQ,QAAQ,OAAO;AAClF,UAAI,IAAI,KAAK;AACb,UAAI,KAAK,SAAS,KAAK,aAAa;AAChC,eAAO,IAAI,KAAK,KAAK,UAAU,KAAK,KAAK,WAAW,CAAC,KAAK;AACtD;AACR,UAAI,IAAI,KAAK,OAAO,OAAO,KAAK,UAAU,CAAC,KAAK,KAAK,KAAK,QAAQ;AAC9D,iBAAS,KAAK,KAAK;AACf,gBAAM,KAAK,CAAC;AAChB,cAAM,KAAK,IAAI,KAAK,UAAU,GAAG,YAAY,KAAK,KAAK,GAAG,YAAY,CAAC,CAAC;AACxE,WAAG,SAAS;AACZ;AAAA,MACJ,OACK;AACD,YAAI,CAAC;AACD,sBAAY,OAAO,GAAG,YAAY,GAAG,GAAG,SAAS;AACrD,iBAAS,KAAK,KAAK;AACf,gBAAM,KAAK,CAAC;AAChB,YAAI,YAAY,GAAG,YAAY,KAAK,SAAS,UAAU,GAAG,YAAY,KAAK,KAAK;AAChF,YAAI,YAAY;AACZ,sBAAY,OAAO,WAAW,OAAO;AAAA,MAC7C;AAAA,IACJ;AACA,OAAG,QAAQ,GAAG,OAAO,cAAc,OAAO,CAAC,IAAI,EAC1C,OAAO,KAAK,YAAY,GAAG,YAAY,IAAI,IAAI,GAAG,IAAI;AAC3D,WAAO;AAAA,EACX;AAAA,EACA,WAAW,IAAI,MAAM;AACjB,QAAI,OAAO,aAAa,IAAI;AAC5B,QAAI,OAAO;AACP,aAAO;AACX,OAAG,aAAa,KAAK,YAAY,KAAK,GAAG;AACzC,OAAG,QAAQ,KAAK,WAAW,GAAG,YAAY,KAAK,KAAK,GAAG,YAAY,KAAK,MAAM,CAAC;AAC/E,SAAK,SAAS,KAAK,MAAM,IAAI;AAC7B,WAAO;AAAA,EACX;AAAA,EACA,eAAe,IAAI,MAAM;AACrB,QAAI,iBAAiB,MAAM,IAAI,KAAK,IAAI;AACpC,aAAO;AACX,QAAI,OAAO,GAAG,YAAY,KAAK;AAC/B,OAAG,SAAS;AACZ,OAAG,QAAQ,KAAK,gBAAgB,IAAI;AACpC,WAAO;AAAA,EACX;AAAA,EACA,WAAW,IAAI,MAAM;AACjB,QAAI,OAAO,aAAa,MAAM,IAAI,KAAK;AACvC,QAAI,OAAO;AACP,aAAO;AACX,QAAI,GAAG,MAAM,QAAQ,KAAK;AACtB,SAAG,aAAa,KAAK,YAAY,KAAK,SAAS,KAAK,IAAI;AAC5D,QAAI,UAAU,cAAc,MAAM,KAAK,MAAM,CAAC;AAC9C,OAAG,aAAa,KAAK,UAAU,KAAK,SAAS,UAAU,KAAK,UAAU;AACtE,OAAG,QAAQ,KAAK,UAAU,GAAG,YAAY,KAAK,KAAK,GAAG,YAAY,KAAK,MAAM,IAAI;AACjF,SAAK,eAAe,OAAO;AAC3B,WAAO;AAAA,EACX;AAAA,EACA,YAAY,IAAI,MAAM;AAClB,QAAI,OAAO,cAAc,MAAM,IAAI,KAAK;AACxC,QAAI,OAAO;AACP,aAAO;AACX,QAAI,GAAG,MAAM,QAAQ,KAAK;AACtB,SAAG,aAAa,KAAK,aAAa,KAAK,SAAS,KAAK,KAAK,WAAW,KAAK,MAAM,OAAO,CAAC,CAAC;AAC7F,QAAI,UAAU,cAAc,MAAM,KAAK,MAAM,IAAI;AACjD,OAAG,aAAa,KAAK,UAAU,KAAK,SAAS,UAAU,KAAK,UAAU;AACtE,OAAG,QAAQ,KAAK,UAAU,GAAG,YAAY,KAAK,KAAK,GAAG,YAAY,KAAK,MAAM,IAAI;AACjF,SAAK,eAAe,OAAO;AAC3B,WAAO;AAAA,EACX;AAAA,EACA,WAAW,IAAI,MAAM;AACjB,QAAI,OAAO,aAAa,IAAI;AAC5B,QAAI,OAAO;AACP,aAAO;AACX,QAAI,MAAM,KAAK,KAAK,OAAO,GAAG,YAAY;AAC1C,QAAI,aAAa,cAAc,KAAK,MAAM,KAAK,KAAK,QAAQ,GAAG,GAAG,QAAQ;AAC1E,WAAO,QAAQ,OAAO,KAAK,KAAK,WAAW,QAAQ,CAAC,KAAK,KAAK;AAC1D;AACJ,QAAI,SAAS,cAAc,SAAS,OAAO,CAAC,MAAM,KAAK,KAAK,WAAW,QAAQ,CAAC,CAAC;AAC7E,cAAQ,KAAK,KAAK;AACtB,QAAI,MAAM,GAAG,OACR,MAAM,KAAK,YAAY,GAAG,IAAI,EAC9B,cAAc,GAAG,OAAO,YAAY,KAAK,KAAK,MAAM,MAAM,OAAO,GAAG,KAAK,GAAG,OAAO,OAAO,CAAC,GAAG,CAAC,IAAI;AACxG,QAAI,QAAQ,KAAK,KAAK;AAClB,UAAI,MAAM,KAAK,YAAY,QAAQ,KAAK,aAAa,GAAG;AAC5D,QAAI,OAAO,IAAI,OAAO,KAAK,cAAc,IAAI,MAAM,KAAK,KAAK,SAAS,GAAG;AACzE,OAAG,SAAS;AACZ,OAAG,QAAQ,MAAM,IAAI;AACrB,WAAO;AAAA,EACX;AAAA,EACA,UAAU,IAAI,MAAM;AAChB,QAAI,OAAO,YAAY,MAAM,IAAI,KAAK;AACtC,QAAI,OAAO;AACP,aAAO;AACX,QAAI,OAAO,GAAG,YAAY,KAAK,KAAK,MAAM,eAAe,IAAI,EAAE,CAAC;AAChE,QAAI,QAAQ,CAAC,GAAG,WAAW,OAAO;AAClC,WAAO,CAAC,IAAI,KAAK,KAAK,IAAI,KAAK,GAAG,SAAS,GAAG;AAC1C,UAAI,KAAK,QAAQ,GAAG,MAAM,QAAQ;AAC9B,mBAAW;AACX;AAAA,MACJ;AACA,eAAS,KAAK,KAAK;AACf,cAAM,KAAK,CAAC;AAAA,IACpB;AACA,QAAI;AACA,SAAG,SAAS;AAChB,QAAI,WAAW,OAAO,aAAa,KAAK,eAAe,OAAO,gBAAgB,KAAK,6BAA6B,KAAK;AACrH,QAAI,KAAK,GAAG,YAAY;AACxB,OAAG,QAAQ,GAAG,OAAO,cAAc,OAAO,CAAC,IAAI,EAAE,OAAO,UAAU,KAAK,IAAI,GAAG,IAAI;AAClF,WAAO;AAAA,EACX;AAAA,EACA,eAAe;AAAA;AACnB;AAMA,IAAM,sBAAN,MAA0B;AAAA,EACtB,YAAY,MAAM;AACd,SAAK,QAAQ;AACb,SAAK,OAAO,CAAC;AACb,SAAK,MAAM;AACX,SAAK,QAAQ,KAAK;AAClB,SAAK,QAAQ,KAAK,OAAO;AAAA,EAC7B;AAAA,EACA,SAAS,IAAI,MAAM,MAAM;AACrB,QAAI,KAAK,SAAS;AACd,aAAO;AACX,QAAI,UAAU,KAAK,UAAU,OAAO,KAAK,MAAM;AAC/C,QAAI,SAAS,KAAK,QAAQ,OAAO;AACjC,QAAI,SAAS,MAAM,SAAS,QAAQ;AAChC,aAAO,KAAK,SAAS,IAAI,MAAM,MAAM;AACzC,WAAO;AAAA,EACX;AAAA,EACA,OAAO,IAAI,MAAM;AACb,SAAK,KAAK,SAAS,KAAyB,KAAK,SAAS,MAA2B,UAAU,KAAK,SAAS,KAAK,GAAG,KAAK,KAAK,QAAQ;AACnI,aAAO,KAAK,SAAS,IAAI,MAAM,KAAK,QAAQ,MAAM;AACtD,WAAO;AAAA,EACX;AAAA,EACA,SAAS,IAAI,MAAM,KAAK;AACpB,OAAG,eAAe,MAAM,IAAI,KAAK,eAAe,KAAK,OAAO,KAAK,QAAQ,KAAK,KAAK,IAAI,CAAC;AACxF,WAAO;AAAA,EACX;AAAA,EACA,UAAUD,MAAK;AACX,QAAIA,MAAK;AACL,WAAK,MAAMA,KAAI,KAAK,KAAK;AACzB,WAAK,KAAK,KAAKA,IAAG;AAClB,WAAK;AACL,aAAO;AAAA,IACX;AACA,QAAIA,SAAQ;AACR,WAAK,QAAQ;AACjB,WAAO;AAAA,EACX;AAAA,EACA,QAAQ,SAAS;AACb,eAAS;AACL,UAAI,KAAK,SAAS,IAA0B;AACxC,eAAO;AAAA,MACX,WACS,KAAK,SAAS,GAAwB;AAC3C,YAAI,CAAC,KAAK,UAAU,eAAe,SAAS,KAAK,KAAK,KAAK,OAAO,IAAI,CAAC;AACnE,iBAAO;AACX,YAAI,QAAQ,WAAW,KAAK,GAAG,KAAK;AAChC,iBAAO,KAAK,QAAQ;AACxB,aAAK,KAAK,KAAK,IAAI,KAAK,UAAU,KAAK,MAAM,KAAK,OAAO,KAAK,MAAM,KAAK,QAAQ,CAAC,CAAC;AACnF,aAAK;AAAA,MACT,WACS,KAAK,SAAS,GAAwB;AAC3C,YAAI,CAAC,KAAK,UAAU,SAAS,SAAS,UAAU,SAAS,KAAK,GAAG,GAAG,KAAK,KAAK,CAAC;AAC3E,iBAAO;AAAA,MACf,WACS,KAAK,SAAS,GAAuB;AAC1C,YAAI,OAAO,UAAU,SAAS,KAAK,GAAG,GAAG,MAAM;AAC/C,YAAI,OAAO,KAAK,KAAK;AACjB,cAAI,QAAQ,eAAe,SAAS,MAAM,KAAK,KAAK;AACpD,cAAI,OAAO;AACP,gBAAI,WAAW,QAAQ,SAAS,MAAM,KAAK,KAAK,KAAK;AACrD,gBAAI,WAAW,GAAG;AACd,mBAAK,UAAU,KAAK;AACpB,oBAAM;AAAA,YACV;AAAA,UACJ;AAAA,QACJ;AACA,YAAI,CAAC;AACD,gBAAM,QAAQ,SAAS,KAAK,GAAG;AACnC,eAAO,MAAM,KAAK,MAAM,QAAQ,SAAS,MAAM;AAAA,MACnD,OACK;AACD,eAAO,QAAQ,SAAS,KAAK,GAAG;AAAA,MACpC;AAAA,IACJ;AAAA,EACJ;AACJ;AACA,SAAS,QAAQ,MAAM,KAAK;AACxB,SAAO,MAAM,KAAK,QAAQ,OAAO;AAC7B,QAAI,OAAO,KAAK,WAAW,GAAG;AAC9B,QAAI,QAAQ;AACR;AACJ,QAAI,CAAC,MAAM,IAAI;AACX,aAAO;AAAA,EACf;AACA,SAAO;AACX;AACA,IAAM,sBAAN,MAA0B;AAAA,EACtB,SAAS,IAAI,MAAM,MAAM;AACrB,QAAI,YAAY,KAAK,QAAQ,GAAG,MAAM,SAAS,KAAK,kBAAkB,IAAI;AAC1E,QAAI,OAAO,KAAK;AAChB,QAAI,YAAY;AACZ,aAAO;AACX,QAAI,gBAAgB,IAAI,KAAK,YAAY,GAAG,YAAY,KAAK,KAAK,GAAG,YAAY,SAAS;AAC1F,OAAG,SAAS;AACZ,OAAG,eAAe,MAAM,IAAI,QAAQ,KAAK,KAAK,iBAAiB,KAAK,gBAAgB,KAAK,OAAO,GAAG,YAAY,GAAG;AAAA,MAC9G,GAAG,GAAG,OAAO,YAAY,KAAK,SAAS,KAAK,KAAK;AAAA,MACjD;AAAA,IACJ,CAAC,CAAC;AACF,WAAO;AAAA,EACX;AAAA,EACA,SAAS;AACL,WAAO;AAAA,EACX;AACJ;AACA,IAAM,oBAAoB;AAAA,EACtB,cAAc,GAAG,MAAM;AAAE,WAAO,KAAK,QAAQ,WAAW,CAAC,KAAK,KAAe,IAAI,oBAAoB,IAAI,IAAI;AAAA,EAAM;AAAA,EACnH,gBAAgB;AAAE,WAAO,IAAI;AAAA,EAAqB;AACtD;AACA,IAAM,iBAAiB;AAAA,EACnB,CAAC,GAAG,SAAS,aAAa,IAAI,KAAK;AAAA,EACnC,CAAC,GAAG,SAAS,aAAa,IAAI,KAAK;AAAA,EACnC,CAAC,GAAG,SAAS,aAAa,IAAI,KAAK;AAAA,EACnC,CAAC,GAAG,SAAS,aAAa,MAAM,GAAG,IAAI,KAAK;AAAA,EAC5C,CAAC,GAAG,SAAS,cAAc,MAAM,GAAG,IAAI,KAAK;AAAA,EAC7C,CAAC,GAAG,SAAS,iBAAiB,MAAM,GAAG,IAAI,KAAK;AAAA,EAChD,CAAC,GAAG,SAAS,YAAY,MAAM,GAAG,IAAI,KAAK;AAC/C;AACA,IAAM,iBAAiB,EAAE,MAAM,IAAI,KAAK,EAAE;AAI1C,IAAM,eAAN,MAAmB;AAAA;AAAA;AAAA;AAAA,EAIf,YAIAE,SAIA,OAAO,WAIP,QAAQ;AACJ,SAAK,SAASA;AACd,SAAK,QAAQ;AACb,SAAK,SAAS;AACd,SAAK,OAAO,IAAI,KAAK;AACrB,SAAK,QAAQ;AAOb,SAAK,oBAAoB,oBAAI;AAC7B,SAAK,YAAY;AAIjB,SAAK,SAAS;AACd,SAAK,KAAK,OAAO,OAAO,SAAS,CAAC,EAAE;AACpC,SAAK,YAAY,KAAK,oBAAoB,KAAK,kBAAkB,OAAO,CAAC,EAAE;AAC3E,SAAK,QAAQ,eAAe,OAAO,KAAK,UAAU,GAAG,KAAK,WAAW,GAAG,CAAC;AACzE,SAAK,QAAQ,CAAC,KAAK,KAAK;AACxB,SAAK,YAAY,UAAU,SAAS,IAAI,eAAe,WAAW,KAAK,IAAI;AAC3E,SAAK,SAAS;AAAA,EAClB;AAAA,EACA,IAAI,YAAY;AACZ,WAAO,KAAK;AAAA,EAChB;AAAA,EACA,UAAU;AACN,QAAI,KAAK,aAAa,QAAQ,KAAK,oBAAoB,KAAK;AACxD,aAAO,KAAK,OAAO;AACvB,QAAI,EAAE,KAAK,IAAI;AACf,eAAS;AACL,eAAS,QAAQ,OAAK;AAClB,YAAI,OAAO,KAAK,QAAQ,KAAK,MAAM,SAAS,KAAK,MAAM,KAAK,MAAM,SAAS,CAAC,IAAI;AAChF,eAAO,QAAQ,KAAK,QAAQ,WAAW,CAAC,QAAQ,KAAK,QAAQ,KAAK,EAAE,OAAO,KAAK,MAAM;AAClF,cAAI,OAAO,KAAK,QAAQ,OAAO;AAC/B,eAAK,QAAQ,KAAK,MAAM,KAAK,MAAM,KAAK,EAAE;AAAA,QAC9C;AACA,YAAI,CAAC;AACD;AACJ,aAAK,cAAc;AAAA,MACvB;AACA,UAAI,KAAK,MAAM,KAAK,KAAK;AACrB;AAEJ,UAAI,CAAC,KAAK,SAAS;AACf,eAAO,KAAK,OAAO;AAAA,IAC3B;AACA,QAAI,KAAK,aAAa,KAAK,cAAc,KAAK,OAAO;AACjD,aAAO;AACX,UAAO,YAAS;AACZ,eAAS,QAAQ,KAAK,OAAO;AACzB,YAAI,MAAM;AACN,cAAI,SAAS,KAAK,MAAM,IAAI;AAC5B,cAAI,UAAU,OAAO;AACjB,gBAAI,UAAU;AACV,qBAAO;AACX,iBAAK,QAAQ;AACb,qBAAS;AAAA,UACb;AAAA,QACJ;AACJ;AAAA,IACJ;AACA,QAAI,OAAO,IAAI,UAAU,KAAK,YAAY,KAAK,KAAK,KAAK,KAAK,MAAM,KAAK,GAAG,CAAC;AAC7E,aAAS,SAAS,KAAK,OAAO;AAC1B,UAAI,OAAO;AACP,YAAIA,UAAS,MAAM,MAAM,IAAI;AAC7B,YAAIA;AACA,eAAK,QAAQ,KAAKA,OAAM;AAAA,MAChC;AACJ,UAAO,QAAO,KAAK,SAAS,GAAG;AAC3B,UAAI,KAAK,OAAO,KAAK,KAAK;AACtB;AACJ,UAAI,KAAK,SAAS,KAAK,aAAa,GAAG;AACnC,iBAAS,QAAQ,KAAK,OAAO;AACzB,cAAI,KAAK,MAAM,MAAM,IAAI;AACrB,kBAAM;AAAA,MAClB;AACA,eAASA,WAAU,KAAK;AACpB,YAAIA,QAAO,SAAS,MAAM,MAAM,IAAI;AAChC,iBAAO;AACf,WAAK,WAAW,OAAO,KAAK,MAAM;AAClC,eAAS,KAAK,KAAK;AACf,aAAK,MAAM,KAAK,CAAC;AAAA,IACzB;AACA,SAAK,WAAW,IAAI;AACpB,WAAO;AAAA,EACX;AAAA,EACA,OAAO,KAAK;AACR,QAAI,KAAK,aAAa,QAAQ,KAAK,YAAY;AAC3C,YAAM,IAAI,WAAW,8BAA8B;AACvD,SAAK,YAAY;AAAA,EACrB;AAAA,EACA,cAAc,OAAO;AACjB,QAAI,CAAC,KAAK,UAAU,OAAO,KAAK,oBAAoB,OAAO,KAAK,iBAAiB,KAC7E,CAAC,KAAK,UAAU,QAAQ,KAAK,MAAM,IAAI;AACvC,aAAO;AACX,QAAI,QAAQ,KAAK,UAAU,UAAU,IAAI;AACzC,QAAI,CAAC;AACD,aAAO;AACX,SAAK,qBAAqB;AAC1B,SAAK,YAAY,WAAW,KAAK,mBAAmB,KAAK,MAAM;AAC/D,SAAK,WAAW;AAChB,QAAI,KAAK,oBAAoB,KAAK,IAAI;AAClC,WAAK;AACL,WAAK;AACL,WAAK,SAAS;AAAA,IAClB,OACK;AACD,WAAK,QAAQ;AACb,WAAK,SAAS;AAAA,IAClB;AACA,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA,EAIA,IAAI,QAAQ;AACR,WAAO,KAAK,MAAM;AAAA,EACtB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,WAAW,QAAQ,KAAK,QAAQ,GAAG;AAC/B,WAAO,KAAK,OAAO,QAAQ,MAAM,KAAK,MAAM,KAAK,EAAE,IAAI;AAAA,EAC3D;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,WAAW;AACP,SAAK,aAAa,KAAK,KAAK,KAAK;AACjC,QAAI,KAAK,mBAAmB,KAAK,IAAI;AACjC,WAAK,oBAAoB,KAAK;AAC9B,WAAK,QAAQ;AACb,WAAK,SAAS;AACd,aAAO;AAAA,IACX,OACK;AACD,WAAK;AACL,WAAK,oBAAoB,KAAK,kBAAkB;AAChD,WAAK,WAAW;AAChB,WAAK,SAAS;AACd,aAAO;AAAA,IACX;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,WAAW;AACP,WAAO,KAAK,SAAS,KAAK,kBAAkB,CAAC,EAAE;AAAA,EACnD;AAAA,EACA,aAAa;AACT,WAAO,KAAK,SAAS,KAAK,OAAO,SAAS,KAAK,KAAK,qBAAqB,KAAK,OAAO,KAAK,MAAM,EAAE,IAAI;AAClG,WAAK;AACL,WAAK,oBAAoB,KAAK,IAAI,KAAK,mBAAmB,KAAK,OAAO,KAAK,MAAM,EAAE,IAAI;AAAA,IAC3F;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,SAAS,OAAO;AACZ,QAAI,IAAI;AACR,MAAE,MAAM;AACR,QAAI,SAAS,KAAK,IAAI;AAClB,QAAE,OAAO;AAAA,IACb,OACK;AACD,QAAE,OAAO,KAAK,YAAY,KAAK;AAC/B,QAAE,OAAO,EAAE,KAAK;AAChB,UAAI,KAAK,OAAO,SAAS,GAAG;AACxB,YAAI,aAAa,KAAK,mBAAmB,SAAS,KAAK;AACvD,eAAO,KAAK,OAAO,MAAM,EAAE,KAAK,EAAE,KAAK;AACnC;AACA,cAAI,WAAW,KAAK,OAAO,MAAM,EAAE;AACnC,cAAI,QAAQ,KAAK,YAAY,QAAQ;AACrC,YAAE,MAAM,WAAW,MAAM;AACzB,YAAE,OAAO,EAAE,KAAK,MAAM,GAAG,KAAK,OAAO,SAAS,CAAC,EAAE,KAAK,UAAU,IAAI;AACpE,uBAAa,EAAE,MAAM,EAAE,KAAK;AAAA,QAChC;AAAA,MACJ;AAAA,IACJ;AACA,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,WAAW;AACP,QAAI,EAAE,KAAK,IAAI,MAAM,EAAE,MAAM,IAAI,IAAI,KAAK,SAAS,KAAK,iBAAiB;AACzE,SAAK,kBAAkB;AACvB,SAAK,MAAM,IAAI;AACf,WAAO,KAAK,QAAQ,KAAK,MAAM,QAAQ,KAAK,SAAS;AACjD,UAAI,KAAK,KAAK,MAAM,KAAK,KAAK,GAAG,UAAU,KAAK,OAAO,kBAAkB,GAAG,IAAI;AAChF,UAAI,CAAC;AACD,cAAM,IAAI,MAAM,6BAA6B,KAAK,GAAG,IAAI,CAAC;AAC9D,UAAI,CAAC,QAAQ,IAAI,MAAM,IAAI;AACvB;AACJ,WAAK,QAAQ;AAAA,IACjB;AAAA,EACJ;AAAA,EACA,YAAY,KAAK;AACb,QAAI,OAAO,KAAK,MAAM,MAAM,GAAG,GAAG;AAClC,QAAI,CAAC,KAAK,MAAM,YAAY;AACxB,UAAI,MAAM,KAAK,QAAQ,IAAI;AAC3B,aAAO,MAAM,IAAI,OAAO,KAAK,MAAM,GAAG,GAAG;AAAA,IAC7C,OACK;AACD,aAAO,QAAQ,OAAO,KAAK;AAAA,IAC/B;AACA,WAAO,MAAM,KAAK,SAAS,KAAK,KAAK,KAAK,MAAM,GAAG,KAAK,KAAK,GAAG,IAAI;AAAA,EACxE;AAAA;AAAA;AAAA;AAAA,EAIA,cAAc;AAAE,WAAO,KAAK,QAAQ,KAAK,YAAY,KAAK,YAAY;AAAA,EAAG;AAAA;AAAA;AAAA;AAAA,EAIzE,aAAa,MAAM,OAAO,QAAQ,GAAG;AACjC,SAAK,QAAQ,eAAe,OAAO,MAAM,OAAO,KAAK,YAAY,OAAO,KAAK,MAAM,MAAM,KAAK,YAAY,KAAK,KAAK,KAAK,MAAM;AAC/H,SAAK,MAAM,KAAK,KAAK,KAAK;AAAA,EAC9B;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,eAAe,MAAM,OAAO,QAAQ,GAAG;AACnC,SAAK,aAAa,KAAK,OAAO,YAAY,IAAI,GAAG,OAAO,KAAK;AAAA,EACjE;AAAA;AAAA;AAAA;AAAA,EAIA,QAAQ,OAAO,MAAM,IAAI;AACrB,QAAI,OAAO,SAAS;AAChB,cAAQ,IAAI,KAAK,KAAK,OAAO,QAAQ,MAAM,KAAK,GAAG,MAAM,OAAO,OAAO,QAAQ,OAAO,SAAS,KAAK,KAAK,YAAY,KAAK,IAAI;AAClI,SAAK,MAAM,SAAS,OAAO,OAAO,KAAK,MAAM,IAAI;AAAA,EACrD;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,WAAWF,MAAK;AACZ,SAAK,MAAM,SAASA,KAAI,OAAO,KAAK,OAAO,OAAO,GAAGA,KAAI,OAAO,KAAK,MAAM,IAAI;AAAA,EACnF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,eAAe,MAAMA,MAAK;AACtB,SAAK,QAAQ,KAAK,OACb,cAAc,YAAYA,KAAI,UAAU,KAAK,KAAK,GAAG,CAACA,KAAI,IAAI,EAC9D,OAAOA,KAAI,MAAMA,KAAI,KAAKA,KAAI,IAAI,GAAGA,KAAI,IAAI;AAAA,EACtD;AAAA;AAAA;AAAA;AAAA,EAIA,gBAAgB;AACZ,QAAI,KAAK,KAAK,MAAM,IAAI;AACxB,QAAI,MAAM,KAAK,MAAM,KAAK,MAAM,SAAS,CAAC;AAC1C,QAAI,SAAS,GAAG,OAAO,KAAK,OAAO,OAAO,GAAG,GAAG,OAAO,IAAI,IAAI;AAC/D,SAAK,QAAQ;AAAA,EACjB;AAAA,EACA,SAAS;AACL,WAAO,KAAK,MAAM,SAAS;AACvB,WAAK,cAAc;AACvB,WAAO,KAAK,QAAQ,KAAK,MAAM,OAAO,KAAK,OAAO,SAAS,KAAK,SAAS,CAAC;AAAA,EAC9E;AAAA,EACA,QAAQ,MAAM;AACV,WAAO,KAAK,OAAO,SAAS,IACxB,WAAW,KAAK,QAAQ,GAAG,KAAK,SAAS,KAAK,OAAO,CAAC,EAAE,MAAM,KAAK,iBAAiB,IAAI;AAAA,EAChG;AAAA;AAAA;AAAA;AAAA,EAIA,WAAW,MAAM;AACb,aAASE,WAAU,KAAK;AACpB,UAAIA,QAAO,OAAO,MAAM,IAAI;AACxB;AACR,QAAI,SAAS,YAAY,KAAK,OAAO,YAAY,KAAK,SAAS,KAAK,KAAK,GAAG,KAAK,KAAK;AACtF,SAAK,QAAQ,KAAK,OACb,cAAc,QAAQ,CAAC,KAAK,KAAK,EACjC,OAAO,KAAK,WAAW,KAAK,QAAQ,MAAM,GAAG,KAAK,KAAK;AAAA,EAChE;AAAA,EACA,IAAI,MAAM,MAAM,IAAI,UAAU;AAC1B,QAAI,OAAO,QAAQ;AACf,aAAO,IAAI,KAAK,OAAO,YAAY,IAAI,GAAG,MAAM,IAAI,QAAQ;AAChE,WAAO,IAAI,YAAY,MAAM,IAAI;AAAA,EACrC;AAAA;AAAA;AAAA;AAAA,EAIA,IAAI,SAAS;AAAE,WAAO,IAAI,OAAO,KAAK,OAAO,OAAO;AAAA,EAAG;AAC3D;AACA,SAAS,WAAW,QAAQ,QAAQ,MAAM,QAAQ,SAAS;AACvD,MAAI,WAAW,OAAO,MAAM,EAAE;AAC9B,MAAI,WAAW,CAAC,GAAG,YAAY,CAAC,GAAG,QAAQ,KAAK,OAAO;AACvD,WAAS,aAAa,MAAM,WAAW;AACnC,WAAO,YAAY,QAAQ,WAAW,OAAO,UAAU;AACnD,UAAI,OAAO,OAAO,SAAS,CAAC,EAAE,OAAO;AACrC,gBAAU;AACV,cAAQ;AACR;AACA,iBAAW,OAAO,MAAM,EAAE;AAAA,IAC9B;AAAA,EACJ;AACA,WAAS,KAAK,KAAK,YAAY,IAAI,KAAK,GAAG,aAAa;AACpD,iBAAa,GAAG,OAAO,QAAQ,IAAI;AACnC,QAAI,OAAO,GAAG,OAAO,QAAQ,MAAM,QAAQ,QAAQ,IAAI,GAAG,IAAI;AAC9D,QAAI,OAAO;AACP,aAAO;AAAA,IACX,WACS,GAAG,KAAK,SAAS,UAAU;AAChC,aAAO,WAAW,QAAQ,QAAQ,IAAI,QAAQ,OAAO;AACrD,mBAAa,GAAG,KAAK,QAAQ,KAAK;AAAA,IACtC,OACK;AACD,aAAO,GAAG,OAAO;AAAA,IACrB;AACA,aAAS,KAAK,IAAI;AAClB,cAAU,KAAK,OAAO,KAAK;AAAA,EAC/B;AACA,eAAa,KAAK,KAAK,QAAQ,KAAK;AACpC,SAAO,IAAI,KAAK,KAAK,MAAM,UAAU,WAAW,KAAK,KAAK,SAAS,OAAO,KAAK,OAAO,KAAK,KAAK,aAAa,MAAS;AAC1H;AAIA,IAAM,iBAAN,MAAM,wBAAuB,OAAO;AAAA;AAAA;AAAA;AAAA,EAIhC,YAKA,SAIA,cAIA,kBAIA,YAIA,cAIA,mBAIA,eAIA,aAIA,UAAU;AACN,UAAM;AACN,SAAK,UAAU;AACf,SAAK,eAAe;AACpB,SAAK,mBAAmB;AACxB,SAAK,aAAa;AAClB,SAAK,eAAe;AACpB,SAAK,oBAAoB;AACzB,SAAK,gBAAgB;AACrB,SAAK,cAAc;AACnB,SAAK,WAAW;AAIhB,SAAK,YAAY,uBAAO,OAAO,IAAI;AACnC,aAAS,KAAK,QAAQ;AAClB,WAAK,UAAU,EAAE,IAAI,IAAI,EAAE;AAAA,EACnC;AAAA,EACA,YAAY,OAAO,WAAW,QAAQ;AAClC,QAAI,QAAQ,IAAI,aAAa,MAAM,OAAO,WAAW,MAAM;AAC3D,aAAS,KAAK,KAAK;AACf,cAAQ,EAAE,OAAO,OAAO,WAAW,MAAM;AAC7C,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA,EAIA,UAAU,MAAM;AACZ,QAAI,SAAS,cAAc,IAAI;AAC/B,QAAI,CAAC;AACD,aAAO;AACX,QAAI,EAAE,SAAS,kBAAkB,IAAI;AACrC,QAAI,eAAe,KAAK,aAAa,MAAM,GAAG,mBAAmB,KAAK,iBAAiB,MAAM,GAAG,aAAa,KAAK,WAAW,MAAM,GAAG,gBAAgB,KAAK,cAAc,MAAM,GAAG,cAAc,KAAK,YAAY,MAAM,GAAG,eAAe,KAAK,aAAa,MAAM,GAAG,WAAW,KAAK;AACpR,QAAI,SAAS,OAAO,WAAW,GAAG;AAC9B,0BAAoB,OAAO,OAAO,CAAC,GAAG,iBAAiB;AACvD,UAAIC,aAAY,QAAQ,MAAM,MAAM,GAAG;AACvC,eAAS,KAAK,OAAO,aAAa;AAC9B,YAAI,EAAE,MAAM,OAAO,WAAW,MAAM,IAAI,OAAO,KAAK,WAAW,EAAE,MAAM,EAAE,IAAI;AAC7E,YAAIA,WAAU,KAAK,OAAK,EAAE,QAAQ,IAAI;AAClC;AACJ,YAAI;AACA,4BAAkBA,WAAU,MAAM,IAC9B,CAAC,IAAI,IAAI,SAAS,UAAU,IAAI,MAAM,GAAG,KAAK;AACtD,YAAI,KAAKA,WAAU;AACnB,YAAI,QAAQ,YAAY,CAAC,SAAS,cAAc,IAAI,CAAC,QAAQ,SACvD,MAAM,KAAK,eAAe,MAAM,KAAK,iBAAiB,CAAC,SAAS,aAAa,SAAS,IAAI,CAAC,SAAS,WAAW;AACrH,QAAAA,WAAU,KAAK,SAAS,OAAO;AAAA,UAC3B;AAAA,UACA;AAAA,UACA,OAAO,SAAS,CAAC,CAAC,SAAS,OAAO,KAAK,CAAC;AAAA,QAC5C,CAAC,CAAC;AACF,YAAI,OAAO;AACP,cAAI,CAAC;AACD,qBAAS,CAAC;AACd,cAAI,MAAM,QAAQ,KAAK,KAAK,iBAAiB;AACzC,mBAAO,IAAI,IAAI;AAAA;AAEf,mBAAO,OAAO,QAAQ,KAAK;AAAA,QACnC;AAAA,MACJ;AACA,gBAAU,IAAI,QAAQA,UAAS;AAC/B,UAAI;AACA,kBAAU,QAAQ,OAAO,UAAU,MAAM,CAAC;AAAA,IAClD;AACA,QAAI,SAAS,OAAO,KAAK;AACrB,gBAAU,QAAQ,OAAO,GAAG,OAAO,KAAK;AAC5C,QAAI,SAAS,OAAO,MAAM,GAAG;AACzB,eAAS,MAAM,OAAO,QAAQ;AAC1B,YAAI,QAAQ,KAAK,WAAW,QAAQ,EAAE,GAAG,SAAS,KAAK,YAAY,QAAQ,EAAE;AAC7E,YAAI,QAAQ;AACR,uBAAa,KAAK,IAAI,iBAAiB,KAAK,IAAI;AACpD,YAAI,SAAS;AACT,wBAAc,MAAM,IAAI;AAAA,MAChC;AAAA,IACJ;AACA,QAAI,SAAS,OAAO,UAAU,GAAG;AAC7B,eAASC,SAAQ,OAAO,YAAY;AAChC,YAAI,QAAQ,WAAW,QAAQA,MAAK,IAAI;AACxC,YAAI,QAAQ,IAAI;AACZ,uBAAa,KAAK,IAAIA,MAAK;AAC3B,2BAAiB,KAAK,IAAIA,MAAK;AAAA,QACnC,OACK;AACD,cAAI,MAAMA,MAAK,SAAS,SAAS,YAAYA,MAAK,MAAM,IAClDA,MAAK,QAAQ,SAAS,YAAYA,MAAK,KAAK,IAAI,IAAI,WAAW,SAAS;AAC9E,uBAAa,OAAO,KAAK,GAAGA,MAAK,KAAK;AACtC,2BAAiB,OAAO,KAAK,GAAGA,MAAK,IAAI;AACzC,qBAAW,OAAO,KAAK,GAAGA,MAAK,IAAI;AAAA,QACvC;AACA,YAAIA,MAAK;AACL,uBAAa,KAAKA,MAAK,OAAO;AAAA,MACtC;AAAA,IACJ;AACA,QAAI,SAAS,OAAO,WAAW,GAAG;AAC9B,eAASA,SAAQ,OAAO,aAAa;AACjC,YAAI,QAAQ,YAAY,QAAQA,MAAK,IAAI;AACzC,YAAI,QAAQ,IAAI;AACZ,wBAAc,KAAK,IAAIA,MAAK;AAAA,QAChC,OACK;AACD,cAAI,MAAMA,MAAK,SAAS,SAAS,aAAaA,MAAK,MAAM,IACnDA,MAAK,QAAQ,SAAS,aAAaA,MAAK,KAAK,IAAI,IAAI,YAAY,SAAS;AAChF,wBAAc,OAAO,KAAK,GAAGA,MAAK,KAAK;AACvC,sBAAY,OAAO,KAAK,GAAGA,MAAK,IAAI;AAAA,QACxC;AAAA,MACJ;AAAA,IACJ;AACA,QAAI,OAAO;AACP,iBAAW,SAAS,OAAO,OAAO,IAAI;AAC1C,WAAO,IAAI,gBAAe,SAAS,cAAc,kBAAkB,YAAY,cAAc,mBAAmB,eAAe,aAAa,QAAQ;AAAA,EACxJ;AAAA;AAAA;AAAA;AAAA,EAIA,YAAY,MAAM;AACd,QAAI,QAAQ,KAAK,UAAU,IAAI;AAC/B,QAAI,SAAS;AACT,YAAM,IAAI,WAAW,sBAAsB,IAAI,GAAG;AACtD,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,YAAY,MAAM,QAAQ;AACtB,QAAI,KAAK,IAAI,cAAc,MAAM,MAAM,MAAM;AAC7C,UAAO,UAAS,MAAM,QAAQ,MAAM,GAAG,OAAM;AACzC,UAAI,OAAO,GAAG,KAAK,GAAG;AACtB,eAAS,SAAS,KAAK;AACnB,YAAI,OAAO;AACP,cAAI,SAAS,MAAM,IAAI,MAAM,GAAG;AAChC,cAAI,UAAU,GAAG;AACb,kBAAM;AACN,qBAAS;AAAA,UACb;AAAA,QACJ;AACJ;AAAA,IACJ;AACA,WAAO,GAAG,eAAe,CAAC;AAAA,EAC9B;AACJ;AACA,SAAS,SAAS,GAAG;AACjB,SAAO,KAAK,QAAQ,EAAE,SAAS;AACnC;AACA,SAAS,cAAc,MAAM;AACzB,MAAI,CAAC,MAAM,QAAQ,IAAI;AACnB,WAAO;AACX,MAAI,KAAK,UAAU;AACf,WAAO;AACX,MAAI,OAAO,cAAc,KAAK,CAAC,CAAC;AAChC,MAAI,KAAK,UAAU;AACf,WAAO;AACX,MAAI,OAAO,cAAc,KAAK,MAAM,CAAC,CAAC;AACtC,MAAI,CAAC,QAAQ,CAAC;AACV,WAAO,QAAQ;AACnB,MAAI,OAAO,CAAC,GAAG,OAAO,KAAK,MAAM,OAAO,KAAK,IAAI;AACjD,MAAI,QAAQ,KAAK,MAAM,QAAQ,KAAK;AACpC,SAAO;AAAA,IACH,OAAO,KAAK,KAAK,OAAO,KAAK,KAAK;AAAA,IAClC,aAAa,KAAK,KAAK,aAAa,KAAK,WAAW;AAAA,IACpD,YAAY,KAAK,KAAK,YAAY,KAAK,UAAU;AAAA,IACjD,aAAa,KAAK,KAAK,aAAa,KAAK,WAAW;AAAA,IACpD,QAAQ,KAAK,KAAK,QAAQ,KAAK,MAAM;AAAA,IACrC,MAAM,CAAC,QAAQ,QAAQ,CAAC,QAAQ,QAC5B,CAAC,OAAO,OAAO,WAAW,WAAW,MAAM,MAAM,OAAO,OAAO,WAAW,MAAM,GAAG,OAAO,WAAW,MAAM;AAAA,EACnH;AACJ;AACA,SAAS,SAAS,OAAO,MAAM;AAC3B,MAAI,QAAQ,MAAM,QAAQ,IAAI;AAC9B,MAAI,QAAQ;AACR,UAAM,IAAI,WAAW,iDAAiD,IAAI,EAAE;AAChF,SAAO;AACX;AACA,IAAI,YAAY,CAAC,SAAS,IAAI;AAC9B,SAAS,IAAI,GAAG,MAAM,OAAO,KAAK,CAAC,GAAG,KAAK;AACvC,YAAU,CAAC,IAAI,SAAS,OAAO;AAAA,IAC3B,IAAI;AAAA,IACJ;AAAA,IACA,OAAO,KAAK,KAAK,SAAS,CAAC,IAAI,CAAC,CAAC,SAAS,OAAO,KAAK,oBAAoB,CAAC,SAAS,cAAc,IAAI,CAAC,SAAS,WAAW,CAAC,CAAC;AAAA,IAC7H,KAAK,QAAQ;AAAA,EACjB,CAAC;AACL;AACA,IAAM,OAAO,CAAC;AACd,IAAM,SAAN,MAAa;AAAA,EACT,YAAY,SAAS;AACjB,SAAK,UAAU;AACf,SAAK,UAAU,CAAC;AAChB,SAAK,QAAQ,CAAC;AAAA,EAClB;AAAA,EACA,MAAM,MAAM,MAAM,IAAI,WAAW,GAAG;AAChC,SAAK,QAAQ,KAAK,MAAM,MAAM,IAAI,IAAI,WAAW,CAAC;AAClD,WAAO;AAAA,EACX;AAAA,EACA,cAAc,MAAM,SAAS,GAAG;AAC5B,aAAS,KAAK;AACV,QAAE,QAAQ,MAAM,MAAM;AAC1B,WAAO;AAAA,EACX;AAAA,EACA,OAAO,MAAM,QAAQ;AACjB,WAAO,KAAK,MAAM;AAAA,MACd,QAAQ,KAAK;AAAA,MACb,SAAS,KAAK;AAAA,MACd,QAAQ,KAAK;AAAA,MACb,OAAO;AAAA,MACP;AAAA,IACJ,CAAC;AAAA,EACL;AACJ;AAIA,IAAM,UAAN,MAAc;AAAA;AAAA;AAAA;AAAA,EAIV,YAKA,MAIA,MAIA,IAIA,WAAW,MAAM;AACb,SAAK,OAAO;AACZ,SAAK,OAAO;AACZ,SAAK,KAAK;AACV,SAAK,WAAW;AAAA,EACpB;AAAA;AAAA;AAAA;AAAA,EAIA,QAAQ,KAAK,QAAQ;AACjB,QAAI,WAAW,IAAI,QAAQ;AAC3B,QAAI,cAAc,KAAK,UAAU,MAAM;AACvC,QAAI,QAAQ,KAAK,KAAK,MAAM,KAAK,OAAO,QAAQ,KAAK,KAAK,QAAQ,IAAI,QAAQ,SAAS,IAAI,QAAQ;AAAA,EACvG;AAAA;AAAA;AAAA;AAAA,EAIA,OAAO,SAAS;AACZ,WAAO,IAAI,OAAO,OAAO,EAAE,cAAc,KAAK,UAAU,CAAC,KAAK,IAAI,EAAE,OAAO,KAAK,MAAM,KAAK,KAAK,KAAK,IAAI;AAAA,EAC7G;AACJ;AACA,IAAM,cAAN,MAAkB;AAAA,EACd,YAAY,MAAM,MAAM;AACpB,SAAK,OAAO;AACZ,SAAK,OAAO;AAAA,EAChB;AAAA,EACA,IAAI,KAAK;AAAE,WAAO,KAAK,OAAO,KAAK,KAAK;AAAA,EAAQ;AAAA,EAChD,IAAI,OAAO;AAAE,WAAO,KAAK,KAAK,KAAK;AAAA,EAAI;AAAA,EACvC,IAAI,WAAW;AAAE,WAAO;AAAA,EAAM;AAAA,EAC9B,QAAQ,KAAK,QAAQ;AACjB,QAAI,MAAM,KAAK,KAAK,IAAI;AACxB,QAAI,QAAQ,KAAK,IAAI,MAAM,SAAS,GAAG,KAAK,OAAO,QAAQ,KAAK,KAAK,QAAQ,EAAE;AAAA,EACnF;AAAA,EACA,SAAS;AAAE,WAAO,KAAK;AAAA,EAAM;AACjC;AACA,SAAS,IAAI,MAAM,MAAM,IAAI,UAAU;AACnC,SAAO,IAAI,QAAQ,MAAM,MAAM,IAAI,QAAQ;AAC/C;AACA,IAAM,qBAAqB,EAAE,SAAS,YAAY,MAAM,eAAe;AACvE,IAAM,mBAAmB,EAAE,SAAS,YAAY,MAAM,eAAe;AACrE,IAAM,YAAY,CAAC;AAAnB,IAAsB,aAAa,CAAC;AACpC,IAAM,kBAAN,MAAsB;AAAA,EAClB,YAAY,MAAM,MAAM,IAAI,MAAM;AAC9B,SAAK,OAAO;AACZ,SAAK,OAAO;AACZ,SAAK,KAAK;AACV,SAAK,OAAO;AAAA,EAChB;AACJ;AACA,IAAM,YAAY;AAClB,IAAI,cAAc;AAClB,IAAI;AACA,gBAAc,IAAI,OAAO,mBAAmB,GAAG;AACnD,SACO,GAAG;AAAE;AACZ,IAAM,gBAAgB;AAAA,EAClB,OAAO,IAAI,MAAM,OAAO;AACpB,QAAI,QAAQ,MAAiB,SAAS,GAAG,MAAM;AAC3C,aAAO;AACX,QAAI,UAAU,GAAG,KAAK,QAAQ,CAAC;AAC/B,aAAS,IAAI,GAAG,IAAI,UAAU,QAAQ;AAClC,UAAI,UAAU,WAAW,CAAC,KAAK;AAC3B,eAAO,GAAG,OAAO,IAAI,KAAK,QAAQ,OAAO,QAAQ,CAAC,CAAC;AAC3D,WAAO;AAAA,EACX;AAAA,EACA,OAAO,IAAI,MAAM,OAAO;AACpB,QAAI,QAAQ;AACR,aAAO;AACX,QAAI,IAAI,6BAA6B,KAAK,GAAG,MAAM,QAAQ,GAAG,QAAQ,EAAE,CAAC;AACzE,WAAO,IAAI,GAAG,OAAO,IAAI,KAAK,QAAQ,OAAO,QAAQ,IAAI,EAAE,CAAC,EAAE,MAAM,CAAC,IAAI;AAAA,EAC7E;AAAA,EACA,WAAW,IAAI,MAAM,OAAO;AACxB,QAAI,QAAQ,MAAgB,SAAS,GAAG,KAAK,QAAQ,CAAC,KAAK;AACvD,aAAO;AACX,QAAI,MAAM,QAAQ;AAClB,WAAO,MAAM,GAAG,OAAO,GAAG,KAAK,GAAG,KAAK;AACnC;AACJ,QAAI,OAAO,MAAM,OAAO,UAAU;AAClC,WAAO,MAAM,GAAG,KAAK,OAAO;AACxB,UAAI,GAAG,KAAK,GAAG,KAAK,IAAI;AACpB;AACA,YAAI,WAAW,QAAQ,GAAG,KAAK,MAAM,CAAC,KAAK;AACvC,iBAAO,GAAG,OAAO,IAAI,KAAK,YAAY,OAAO,MAAM,GAAG;AAAA,YAClD,IAAI,KAAK,UAAU,OAAO,QAAQ,IAAI;AAAA,YACtC,IAAI,KAAK,UAAU,MAAM,IAAI,MAAM,MAAM,CAAC;AAAA,UAC9C,CAAC,CAAC;AAAA,MACV,OACK;AACD,kBAAU;AAAA,MACd;AAAA,IACJ;AACA,WAAO;AAAA,EACX;AAAA,EACA,QAAQ,IAAI,MAAM,OAAO;AACrB,QAAI,QAAQ,MAAgB,SAAS,GAAG,MAAM;AAC1C,aAAO;AACX,QAAI,QAAQ,GAAG,MAAM,QAAQ,GAAG,GAAG,GAAG;AACtC,QAAI,MAAM,sIAAsI,KAAK,KAAK;AAC1J,QAAI,KAAK;AACL,aAAO,GAAG,OAAO,IAAI,KAAK,UAAU,OAAO,QAAQ,IAAI,IAAI,CAAC,EAAE,QAAQ;AAAA,QAClE,IAAI,KAAK,UAAU,OAAO,QAAQ,CAAC;AAAA;AAAA,QAEnC,IAAI,KAAK,KAAK,QAAQ,GAAG,QAAQ,IAAI,CAAC,EAAE,MAAM;AAAA,QAC9C,IAAI,KAAK,UAAU,QAAQ,IAAI,CAAC,EAAE,QAAQ,QAAQ,IAAI,IAAI,CAAC,EAAE,MAAM;AAAA,MACvE,CAAC,CAAC;AAAA,IACN;AACA,QAAI,UAAU,+BAA+B,KAAK,KAAK;AACvD,QAAI;AACA,aAAO,GAAG,OAAO,IAAI,KAAK,SAAS,OAAO,QAAQ,IAAI,QAAQ,CAAC,EAAE,MAAM,CAAC;AAC5E,QAAI,WAAW,cAAc,KAAK,KAAK;AACvC,QAAI;AACA,aAAO,GAAG,OAAO,IAAI,KAAK,uBAAuB,OAAO,QAAQ,IAAI,SAAS,CAAC,EAAE,MAAM,CAAC;AAC3F,QAAI,IAAI,mKAAmK,KAAK,KAAK;AACrL,QAAI,CAAC;AACD,aAAO;AACX,WAAO,GAAG,OAAO,IAAI,KAAK,SAAS,OAAO,QAAQ,IAAI,EAAE,CAAC,EAAE,MAAM,CAAC;AAAA,EACtE;AAAA,EACA,SAAS,IAAI,MAAM,OAAO;AACtB,QAAI,QAAQ,MAAM,QAAQ;AACtB,aAAO;AACX,QAAI,MAAM,QAAQ;AAClB,WAAO,GAAG,KAAK,GAAG,KAAK;AACnB;AACJ,QAAI,SAAS,GAAG,MAAM,QAAQ,GAAG,KAAK,GAAG,QAAQ,GAAG,MAAM,KAAK,MAAM,CAAC;AACtE,QAAI,UAAU,YAAY,KAAK,MAAM,GAAG,SAAS,YAAY,KAAK,KAAK;AACvE,QAAI,UAAU,QAAQ,KAAK,MAAM,GAAG,SAAS,QAAQ,KAAK,KAAK;AAC/D,QAAI,eAAe,CAAC,WAAW,CAAC,UAAU,WAAW;AACrD,QAAI,gBAAgB,CAAC,YAAY,CAAC,WAAW,UAAU;AACvD,QAAI,UAAU,iBAAiB,QAAQ,MAAM,CAAC,iBAAiB;AAC/D,QAAI,WAAW,kBAAkB,QAAQ,MAAM,CAAC,gBAAgB;AAChE,WAAO,GAAG,OAAO,IAAI,gBAAgB,QAAQ,KAAK,qBAAqB,kBAAkB,OAAO,MAAM,UAAU,IAAoB,MAAsB,WAAW,IAAqB,EAAkB,CAAC;AAAA,EACjN;AAAA,EACA,UAAU,IAAI,MAAM,OAAO;AACvB,QAAI,QAAQ,MAAiB,GAAG,KAAK,QAAQ,CAAC,KAAK;AAC/C,aAAO,GAAG,OAAO,IAAI,KAAK,WAAW,OAAO,QAAQ,CAAC,CAAC;AAC1D,QAAI,QAAQ,IAAI;AACZ,UAAI,MAAM,QAAQ;AAClB,aAAO,GAAG,KAAK,GAAG,KAAK;AACnB;AACJ,UAAI,GAAG,KAAK,GAAG,KAAK,MAAM,OAAO,QAAQ;AACrC,eAAO,GAAG,OAAO,IAAI,KAAK,WAAW,OAAO,MAAM,CAAC,CAAC;AAAA,IAC5D;AACA,WAAO;AAAA,EACX;AAAA,EACA,KAAK,IAAI,MAAM,OAAO;AAClB,WAAO,QAAQ,KAAe,GAAG,OAAO,IAAI;AAAA,MAAgB;AAAA,MAAW;AAAA,MAAO,QAAQ;AAAA,MAAG;AAAA;AAAA,IAAiB,CAAC,IAAI;AAAA,EACnH;AAAA,EACA,MAAM,IAAI,MAAM,OAAO;AACnB,WAAO,QAAQ,MAAgB,GAAG,KAAK,QAAQ,CAAC,KAAK,KAC/C,GAAG,OAAO,IAAI;AAAA,MAAgB;AAAA,MAAY;AAAA,MAAO,QAAQ;AAAA,MAAG;AAAA;AAAA,IAAiB,CAAC,IAAI;AAAA,EAC5F;AAAA,EACA,QAAQ,IAAI,MAAM,OAAO;AACrB,QAAI,QAAQ;AACR,aAAO;AAEX,aAAS,IAAI,GAAG,MAAM,SAAS,GAAG,KAAK,GAAG,KAAK;AAC3C,UAAI,OAAO,GAAG,MAAM,CAAC;AACrB,UAAI,gBAAgB,oBAAoB,KAAK,QAAQ,aAAa,KAAK,QAAQ,aAAa;AAGxF,YAAI,CAAC,KAAK,QAAQ,GAAG,UAAU,KAAK,EAAE,KAAK,SAAS,CAAC,QAAQ,KAAK,GAAG,MAAM,QAAQ,GAAG,QAAQ,CAAC,CAAC,GAAG;AAC/F,aAAG,MAAM,CAAC,IAAI;AACd,iBAAO;AAAA,QACX;AAGA,YAAI,UAAU,GAAG,YAAY,CAAC;AAC9B,YAAI,OAAO,GAAG,MAAM,CAAC,IAAI,WAAW,IAAI,SAAS,KAAK,QAAQ,YAAY,KAAK,OAAO,KAAK,OAAO,KAAK,MAAM,QAAQ,CAAC;AAEtH,YAAI,KAAK,QAAQ;AACb,mBAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AACxB,gBAAI,IAAI,GAAG,MAAM,CAAC;AAClB,gBAAI,aAAa,mBAAmB,EAAE,QAAQ;AAC1C,gBAAE,OAAO;AAAA,UACjB;AACJ,eAAO,KAAK;AAAA,MAChB;AAAA,IACJ;AACA,WAAO;AAAA,EACX;AACJ;AACA,SAAS,WAAW,IAAI,SAAS,MAAM,OAAO,UAAU;AACpD,MAAI,EAAE,KAAK,IAAI,IAAI,OAAO,GAAG,KAAK,QAAQ,GAAG,SAAS;AACtD,UAAQ,QAAQ,IAAI,KAAK,UAAU,OAAO,SAAS,QAAQ,KAAK,QAAQ,IAAI,EAAE,CAAC;AAC/E,UAAQ,KAAK,IAAI,KAAK,UAAU,WAAW,GAAG,QAAQ,CAAC;AACvD,MAAI,QAAQ,IAAc;AACtB,QAAI,MAAM,GAAG,UAAU,WAAW,CAAC;AACnC,QAAI,OAAO,SAAS,MAAM,MAAM,GAAG,QAAQ,GAAG,MAAM,GAAG;AACvD,QAAI,MAAM;AACN,YAAM,GAAG,UAAU,KAAK,EAAE;AAE1B,UAAI,OAAO,KAAK,IAAI;AAChB,gBAAQ,eAAe,MAAM,MAAM,GAAG,QAAQ,GAAG,MAAM;AACvD,YAAI;AACA,gBAAM,GAAG,UAAU,MAAM,EAAE;AAAA,MACnC;AAAA,IACJ;AACA,QAAI,GAAG,KAAK,GAAG,KAAK,IAAc;AAC9B,cAAQ,KAAK,IAAI,KAAK,UAAU,UAAU,WAAW,CAAC,CAAC;AACvD,eAAS,MAAM;AACf,UAAI;AACA,gBAAQ,KAAK,IAAI;AACrB,UAAI;AACA,gBAAQ,KAAK,KAAK;AACtB,cAAQ,KAAK,IAAI,KAAK,UAAU,KAAK,MAAM,CAAC;AAAA,IAChD;AAAA,EACJ,WACS,QAAQ,IAAc;AAC3B,QAAI,QAAQ,eAAe,MAAM,WAAW,GAAG,QAAQ,GAAG,QAAQ,KAAK;AACvE,QAAI,OAAO;AACP,cAAQ,KAAK,KAAK;AAClB,eAAS,MAAM;AAAA,IACnB;AAAA,EACJ;AACA,SAAO,IAAI,MAAM,OAAO,QAAQ,OAAO;AAC3C;AAIA,SAAS,SAAS,MAAM,OAAO,QAAQ;AACnC,MAAI,OAAO,KAAK,WAAW,KAAK;AAChC,MAAI,QAAQ,IAAc;AACtB,aAAS,MAAM,QAAQ,GAAG,MAAM,KAAK,QAAQ,OAAO;AAChD,UAAI,KAAK,KAAK,WAAW,GAAG;AAC5B,UAAI,MAAM;AACN,eAAO,IAAI,KAAK,KAAK,QAAQ,QAAQ,MAAM,IAAI,MAAM;AACzD,UAAI,MAAM,MAAM,MAAM;AAClB,eAAO;AAAA,IACf;AACA,WAAO;AAAA,EACX,OACK;AACD,QAAI,QAAQ,GAAG,MAAM;AACrB,aAAS,UAAU,OAAO,MAAM,KAAK,QAAQ,OAAO;AAChD,UAAI,KAAK,KAAK,WAAW,GAAG;AAC5B,UAAI,MAAM,EAAE,GAAG;AACX;AAAA,MACJ,WACS,SAAS;AACd,kBAAU;AAAA,MACd,WACS,MAAM,IAAc;AACzB;AAAA,MACJ,WACS,MAAM,IAAc;AACzB,YAAI,CAAC;AACD;AACJ;AAAA,MACJ,WACS,MAAM,IAAe;AAC1B,kBAAU;AAAA,MACd;AAAA,IACJ;AACA,WAAO,MAAM,QAAQ,IAAI,KAAK,KAAK,QAAQ,QAAQ,MAAM,MAAM,IAAI,OAAO,KAAK,SAAS,OAAO;AAAA,EACnG;AACJ;AACA,SAAS,eAAe,MAAM,OAAO,QAAQ;AACzC,MAAI,OAAO,KAAK,WAAW,KAAK;AAChC,MAAI,QAAQ,MAAM,QAAQ,MAAM,QAAQ;AACpC,WAAO;AACX,MAAI,MAAM,QAAQ,KAAK,KAAK;AAC5B,WAAS,MAAM,QAAQ,GAAG,UAAU,OAAO,MAAM,KAAK,QAAQ,OAAO;AACjE,QAAI,KAAK,KAAK,WAAW,GAAG;AAC5B,QAAI;AACA,gBAAU;AAAA,aACL,MAAM;AACX,aAAO,IAAI,KAAK,WAAW,QAAQ,QAAQ,MAAM,IAAI,MAAM;AAAA,aACtD,MAAM;AACX,gBAAU;AAAA,EAClB;AACA,SAAO;AACX;AACA,SAAS,eAAe,MAAM,OAAO,QAAQ,cAAc;AACvD,WAAS,UAAU,OAAO,MAAM,QAAQ,GAAG,MAAM,KAAK,IAAI,KAAK,QAAQ,MAAM,GAAG,GAAG,MAAM,KAAK,OAAO;AACjG,QAAI,KAAK,KAAK,WAAW,GAAG;AAC5B,QAAI;AACA,gBAAU;AAAA,aACL,MAAM;AACX,aAAO,eAAe,QAAQ,IAAI,KAAK,WAAW,QAAQ,QAAQ,MAAM,IAAI,MAAM;AAAA,SACjF;AACD,UAAI,gBAAgB,CAAC,MAAM,EAAE;AACzB,uBAAe;AACnB,UAAI,MAAM;AACN,eAAO;AAAA,eACF,MAAM;AACX,kBAAU;AAAA,IAClB;AAAA,EACJ;AACA,SAAO;AACX;AAKA,IAAM,gBAAN,MAAoB;AAAA;AAAA;AAAA;AAAA,EAIhB,YAIAF,SAIA,MAIA,QAAQ;AACJ,SAAK,SAASA;AACd,SAAK,OAAO;AACZ,SAAK,SAAS;AAId,SAAK,QAAQ,CAAC;AAAA,EAClB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,KAAK,KAAK;AAAE,WAAO,OAAO,KAAK,MAAM,KAAK,KAAK,KAAK,WAAW,MAAM,KAAK,MAAM;AAAA,EAAG;AAAA;AAAA;AAAA;AAAA,EAInF,IAAI,MAAM;AAAE,WAAO,KAAK,SAAS,KAAK,KAAK;AAAA,EAAQ;AAAA;AAAA;AAAA;AAAA;AAAA,EAKnD,MAAM,MAAM,IAAI;AAAE,WAAO,KAAK,KAAK,MAAM,OAAO,KAAK,QAAQ,KAAK,KAAK,MAAM;AAAA,EAAG;AAAA;AAAA;AAAA;AAAA,EAIhF,OAAOF,MAAK;AACR,SAAK,MAAM,KAAKA,IAAG;AACnB,WAAOA,KAAI;AAAA,EACf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,aAAa,MAAM,MAAM,IAAI,MAAM,OAAO;AACtC,WAAO,KAAK,OAAO,IAAI,gBAAgB,MAAM,MAAM,KAAK,OAAO,IAAoB,MAAsB,QAAQ,IAAqB,EAAkB,CAAC;AAAA,EAC7J;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,cAAc;AACd,aAAS,IAAI,KAAK,MAAM,SAAS,GAAG,KAAK,GAAG,KAAK;AAC7C,UAAI,OAAO,KAAK,MAAM,CAAC;AACvB,UAAI,gBAAgB,oBAAoB,KAAK,QAAQ,aAAa,KAAK,QAAQ;AAC3E,eAAO;AAAA,IACf;AACA,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA,EAIA,WAAWA,MAAK;AACZ,WAAO,KAAK,OAAOA,IAAG;AAAA,EAC1B;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,eAAe,MAAM;AAEjB,aAAS,IAAI,MAAM,IAAI,KAAK,MAAM,QAAQ,KAAK;AAC3C,UAAI,QAAQ,KAAK,MAAM,CAAC;AACxB,UAAI,EAAE,iBAAiB,mBAAmB,MAAM,KAAK,WAAY,MAAM,OAAO;AAC1E;AACJ,UAAI,MAAM,MAAM,QAAQ,sBAAsB,MAAM,QAAQ;AAC5D,UAAI,YAAY,MAAM,KAAK,MAAM;AACjC,UAAI,MAAM,IAAI,IAAI;AAElB,aAAO,KAAK,MAAM,KAAK;AACnB,YAAI,OAAO,KAAK,MAAM,CAAC;AACvB,YAAI,gBAAgB,mBAAoB,KAAK,OAAO,KAAsB,KAAK,QAAQ,MAAM;AAAA,QAEzF,EAAE,QAAS,MAAM,OAAO,KAAuB,KAAK,OAAO,OACtD,KAAK,KAAK,KAAK,OAAO,aAAa,KAAK,OAAO,KAAK,KAAK,KAAK,QAAQ,KAAK,YAAY,KAAK;AACjG,iBAAO;AACP;AAAA,QACJ;AAAA,MACJ;AACA,UAAI,CAAC;AACD;AACJ,UAAI,OAAO,MAAM,KAAK,SAAS,UAAU,CAAC;AAC1C,UAAI,QAAQ,KAAK,MAAM,MAAM,MAAM;AAGnC,UAAI,KAAK;AACL,YAAI,OAAO,KAAK,IAAI,GAAG,KAAK,KAAK,KAAK,MAAM,SAAS;AACrD,gBAAQ,KAAK,KAAK;AAClB,cAAM,MAAM,OAAO;AACnB,eAAO,QAAQ,IAAI,aAAa;AAAA,MACpC;AAEA,UAAI,KAAK,KAAK;AACV,gBAAQ,KAAK,KAAK,IAAI,KAAK,KAAK,MAAM,OAAO,KAAK,EAAE,CAAC;AACzD,eAAS,IAAI,IAAI,GAAG,IAAI,GAAG,KAAK;AAC5B,YAAI,KAAK,MAAM,CAAC,aAAa;AACzB,kBAAQ,KAAK,KAAK,MAAM,CAAC,CAAC;AAC9B,aAAK,MAAM,CAAC,IAAI;AAAA,MACpB;AACA,UAAI,MAAM,KAAK;AACX,gBAAQ,KAAK,KAAK,IAAI,MAAM,KAAK,MAAM,MAAM,MAAM,GAAG,CAAC;AAC3D,UAAI,UAAU,KAAK,IAAI,MAAM,OAAO,KAAK,OAAO;AAEhD,WAAK,MAAM,CAAC,IAAI,OAAO,KAAK,QAAQ,QAAQ,IAAI,gBAAgB,KAAK,MAAM,KAAK,MAAM,OAAO,KAAK,IAAI,IAAI;AAC1G,UAAI,OAAO,KAAK,MAAM,CAAC,IAAI,OAAO,MAAM,MAAM,MAAM,IAAI,gBAAgB,MAAM,MAAM,KAAK,MAAM,IAAI,MAAM,IAAI,IAAI;AAEjH,UAAI;AACA,aAAK,MAAM,OAAO,GAAG,GAAG,OAAO;AAAA;AAE/B,aAAK,MAAM,CAAC,IAAI;AAAA,IACxB;AAEA,QAAI,SAAS,CAAC;AACd,aAAS,IAAI,MAAM,IAAI,KAAK,MAAM,QAAQ,KAAK;AAC3C,UAAI,OAAO,KAAK,MAAM,CAAC;AACvB,UAAI,gBAAgB;AAChB,eAAO,KAAK,IAAI;AAAA,IACxB;AACA,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,qBAAqB,MAAM;AACvB,aAAS,IAAI,KAAK,MAAM,SAAS,GAAG,KAAK,GAAG,KAAK;AAC7C,UAAI,OAAO,KAAK,MAAM,CAAC;AACvB,UAAI,gBAAgB,mBAAmB,KAAK,QAAQ;AAChD,eAAO;AAAA,IACf;AACA,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,YAAY,YAAY;AACpB,QAAI,UAAU,KAAK,eAAe,UAAU;AAC5C,SAAK,MAAM,SAAS;AACpB,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,UAAU,MAAM;AAAE,WAAO,UAAU,KAAK,MAAM,OAAO,KAAK,MAAM,IAAI,KAAK;AAAA,EAAQ;AAAA,EACjF,IAAI,MAAM,MAAM,IAAI,UAAU;AAC1B,QAAI,OAAO,QAAQ;AACf,aAAO,IAAI,KAAK,OAAO,YAAY,IAAI,GAAG,MAAM,IAAI,QAAQ;AAChE,WAAO,IAAI,YAAY,MAAM,IAAI;AAAA,EACrC;AACJ;AACA,SAAS,YAAY,UAAU,OAAO;AAClC,MAAI,CAAC,MAAM;AACP,WAAO;AACX,MAAI,CAAC,SAAS;AACV,WAAO;AACX,MAAI,OAAO,SAAS,MAAM,GAAG,KAAK;AAClC,WAAS,QAAQ,OAAO;AACpB,WAAO,KAAK,KAAK,UAAU,KAAK,EAAE,EAAE,KAAK,KAAK;AAC1C;AACJ,QAAI,KAAK,KAAK,UAAU,KAAK,EAAE,EAAE,OAAO,KAAK,MAAM;AAC/C,UAAI,IAAI,KAAK,EAAE;AACf,UAAI,aAAa;AACb,aAAK,EAAE,IAAI,IAAI,QAAQ,EAAE,MAAM,EAAE,MAAM,EAAE,IAAI,YAAY,EAAE,UAAU,CAAC,IAAI,CAAC,CAAC;AAAA,IACpF,OACK;AACD,WAAK,OAAO,MAAM,GAAG,IAAI;AAAA,IAC7B;AAAA,EACJ;AACA,SAAO;AACX;AAGA,IAAM,UAAU,CAAC,KAAK,WAAW,KAAK,UAAU,KAAK,aAAa,KAAK,UAAU;AACjF,IAAM,iBAAN,MAAqB;AAAA,EACjB,YAAY,WAAW,OAAO;AAC1B,SAAK,YAAY;AACjB,SAAK,QAAQ;AAEb,SAAK,IAAI;AAET,SAAK,WAAW;AAChB,SAAK,cAAc;AAGnB,SAAK,SAAS;AACd,QAAI,UAAU;AACV,WAAK,WAAW,UAAU,KAAK,GAAG;AAAA,EAC1C;AAAA,EACA,eAAe;AACX,SAAK,WAAW,KAAK,IAAI,KAAK,UAAU,SAAS,KAAK,UAAU,KAAK,GAAG,IAAI;AAC5E,SAAK,SAAS;AACd,SAAK,cAAc;AAAA,EACvB;AAAA,EACA,OAAO,KAAK,WAAW;AACnB,WAAO,KAAK,YAAY,KAAK,SAAS,MAAM;AACxC,WAAK,aAAa;AACtB,QAAI,CAAC,KAAK,YAAY,KAAK,SAAS,QAAQ,MAAM,MAAM,IAAI;AACxD,aAAO;AACX,QAAI,KAAK,cAAc,GAAG;AACtB,UAAI,MAAM,KAAK,SAAS;AACxB,aAAO,MAAM,KAAK,KAAK,MAAM,KAAK,MAAM,GAAG,GAAG,KAAK;AAC/C;AACJ,WAAK,cAAc,MAAM,MAAM,IAAI;AAAA,IACvC;AACA,QAAI,IAAI,KAAK;AACb,QAAI,CAAC,GAAG;AACJ,UAAI,KAAK,SAAS,KAAK,SAAS,KAAK,OAAO;AAC5C,QAAE,WAAW;AAAA,IACjB;AACA,QAAI,OAAO,MAAM,KAAK,SAAS;AAC/B,WAAO,EAAE,MAAM;AACX,UAAI,CAAC,EAAE,OAAO;AACV,eAAO;AACf,eAAS;AACL,UAAI,EAAE,QAAQ;AACV,eAAO,KAAK,SAAS,QAAQ;AACjC,UAAI,CAAC,EAAE,WAAW,IAAI;AAClB,eAAO;AAAA,IACf;AAAA,EACJ;AAAA,EACA,QAAQ,MAAM;AACV,QAAI,OAAO,KAAK,OAAO;AACvB,WAAO,QAAQ,KAAK,KAAK,SAAS,WAAW,KAAK;AAAA,EACtD;AAAA,EACA,UAAU,IAAI;AACV,QAAI,MAAM,KAAK,QAAQ,MAAM,KAAK,SAAS,QAAQ,UAAU,KAAK,eAAe,KAAK,SAAS,UAAU,IAAI;AAC7G,QAAI,QAAQ,GAAG,mBAAmB,MAAM,OAAO,SAAS,GAAG,MAAM,SAAS;AAC1E,QAAI,UAAU,KAAK,QAAQ;AAC3B,eAAS;AACL,UAAI,IAAI,KAAK,MAAM,SAAS;AACxB,YAAI,IAAI,KAAK,eAAe,IAAI,WAAW;AACvC;AACJ;AAAA,MACJ;AACA,UAAI,MAAM,WAAW,IAAI,OAAO,KAAK,GAAG,MAAM;AAC9C,UAAI,IAAI,KAAK,OAAO,GAAG,OAAO,GAAG,MAAM,EAAE,IAAI;AACzC,WAAG,QAAQ,IAAI,MAAM,GAAG;AAAA,MAC5B,OACK;AACD,YAAI,QAAQ,IAAI,KAAK,GAAG,OAAO,QAAQ,MAAM,KAAK,SAAS,GAAG,CAAC,GAAG,CAAC,GAAG,GAAG,GAAG,MAAM,QAAQ;AAC1F,WAAG,kBAAkB,IAAI,OAAO,IAAI,IAAI;AACxC,WAAG,QAAQ,OAAO,GAAG;AAAA,MACzB;AAKA,UAAI,IAAI,KAAK,GAAG,OAAO,GAAG;AACtB,YAAI,QAAQ,QAAQ,IAAI,KAAK,EAAE,IAAI,GAAG;AAClC,gBAAM,IAAI,KAAK;AACf,mBAAS,GAAG,MAAM,SAAS;AAAA,QAC/B,OACK;AACD,gBAAM;AACN,mBAAS;AACT,oBAAU,IAAI,KAAK;AACnB,kBAAQ,GAAG,MAAM,SAAS;AAAA,QAC9B;AAAA,MACJ;AACA,UAAI,CAAC,IAAI,YAAY;AACjB;AAAA,IACR;AACA,WAAO,GAAG,MAAM,SAAS,SAAS,QAAQ;AACtC,SAAG,MAAM,SAAS,IAAI;AACtB,SAAG,MAAM,UAAU,IAAI;AAAA,IAC3B;AACA,WAAO,MAAM;AAAA,EACjB;AACJ;AAIA,SAAS,WAAW,KAAK,QAAQ;AAC7B,MAAI,MAAM;AACV,WAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACpC,QAAI,UAAU,OAAO,IAAI,CAAC,EAAE,IAAI,QAAQ,OAAO,CAAC,EAAE;AAClD,QAAI,UAAU;AACV,aAAO,QAAQ;AAAA,EACvB;AACA,SAAO;AACX;AACA,IAAM,uBAAuB,UAAU;AAAA,EACnC,kBAAkB,KAAK;AAAA,EACvB,gBAAgB,KAAK;AAAA,EACrB,sCAAsC,KAAK;AAAA,EAC3C,sCAAsC,KAAK;AAAA,EAC3C,mBAAmB,KAAK;AAAA,EACxB,mBAAmB,KAAK;AAAA,EACxB,mBAAmB,KAAK;AAAA,EACxB,mBAAmB,KAAK;AAAA,EACxB,wBAAwB,KAAK;AAAA,EAC7B,QAAQ,KAAK;AAAA,EACb,QAAQ,KAAK;AAAA,EACb,gBAAgB,KAAK;AAAA,EACrB,sBAAsB,KAAK;AAAA,EAC3B,sBAAsB,KAAK;AAAA,EAC3B,kCAAkC,KAAK;AAAA,EACvC,kBAAkB,KAAK;AAAA,EACvB,uBAAuB,KAAK;AAAA,EAC5B,gBAAgB,KAAK;AAAA,EACrB,0EAA0E,KAAK;AAAA,EAC/E,sBAAsB,KAAK;AAAA,EAC3B,WAAW,KAAK;AAAA,EAChB,WAAW,KAAK;AACpB,CAAC;AAID,IAAM,SAAS,IAAI,eAAe,IAAI,QAAQ,SAAS,EAAE,OAAO,oBAAoB,GAAG,OAAO,KAAK,mBAAmB,EAAE,IAAI,OAAK,oBAAoB,CAAC,CAAC,GAAG,OAAO,KAAK,mBAAmB,EAAE,IAAI,OAAK,kBAAkB,CAAC,CAAC,GAAG,OAAO,KAAK,mBAAmB,GAAG,gBAAgB,mBAAmB,OAAO,KAAK,aAAa,EAAE,IAAI,OAAK,cAAc,CAAC,CAAC,GAAG,OAAO,KAAK,aAAa,GAAG,CAAC,CAAC;AAErX,SAAS,cAAc,MAAM,MAAM,IAAI;AACnC,MAAI,SAAS,CAAC;AACd,WAAS,IAAI,KAAK,YAAY,MAAM,QAAO,IAAI,EAAE,aAAa;AAC1D,QAAI,UAAU,IAAI,EAAE,OAAO;AAC3B,QAAI,UAAU;AACV,aAAO,KAAK,EAAE,MAAM,KAAK,IAAI,QAAQ,CAAC;AAC1C,QAAI,CAAC;AACD;AACJ,UAAM,EAAE;AAAA,EACZ;AACA,SAAO;AACX;AAKA,SAAS,UAAU,QAAQ;AACvB,MAAI,EAAE,YAAY,WAAW,IAAI;AACjC,MAAI,OAAO,WAAW,CAAC,MAAM,UAAU;AACnC,QAAI,KAAK,KAAK,KAAK;AACnB,QAAI,eAAe,MAAM,KAAK,aAAa,MAAM,KAAK,aAAa;AAC/D,UAAI,OAAO;AACX,UAAI,MAAM,KAAK,YAAY;AACvB,YAAI,WAAW,KAAK,KAAK,SAAS,KAAK,QAAQ;AAC/C,YAAI;AACA,iBAAO,MAAM,KAAK,SAAS,MAAM,SAAS,EAAE;AAAA,MACpD;AACA,UAAIE,UAAS,WAAW,IAAI;AAC5B,UAAIA;AACA,eAAO,EAAE,QAAAA,SAAQ,SAAS,CAAAG,UAAQA,MAAK,KAAK,MAAM,KAAK,SAAS;AAAA,IACxE,WACS,eAAe,MAAM,KAAK,aAAa,MAAM,KAAK,WAAW,MAAM,KAAK,eAAe;AAC5F,aAAO,EAAE,QAAQ,YAAY,SAAS,cAAc,KAAK,MAAM,KAAK,MAAM,KAAK,EAAE,EAAE;AAAA,IACvF;AACA,WAAO;AAAA,EACX,CAAC;AACD,SAAO,EAAE,KAAK;AAClB;AAEA,IAAM,qBAAqB,EAAE,SAAS,iBAAiB,MAAM,oBAAoB;AAMjF,IAAM,gBAAgB;AAAA,EAClB,aAAa,CAAC;AAAA,IACN,MAAM;AAAA,IACN,OAAO,EAAE,qBAAqB,KAAK,cAAc;AAAA,EACrD,GAAG;AAAA,IACC,MAAM;AAAA,IACN,OAAO,KAAK;AAAA,EAChB,CAAC;AAAA,EACL,aAAa,CAAC;AAAA,IACN,MAAM;AAAA,IACN,MAAM,IAAI,MAAM,KAAK;AACjB,UAAI,QAAQ,OAAiB,GAAG,KAAK,MAAM,CAAC,KAAK,OAAO,GAAG,KAAK,MAAM,CAAC,KAAK;AACxE,eAAO;AACX,UAAI,SAAS,GAAG,MAAM,MAAM,GAAG,GAAG,GAAG,QAAQ,GAAG,MAAM,MAAM,GAAG,MAAM,CAAC;AACtE,UAAI,UAAU,QAAQ,KAAK,MAAM,GAAG,SAAS,QAAQ,KAAK,KAAK;AAC/D,UAAI,UAAU,YAAY,KAAK,MAAM,GAAG,SAAS,YAAY,KAAK,KAAK;AACvE,aAAO,GAAG,aAAa,oBAAoB,KAAK,MAAM,GAAG,CAAC,WAAW,CAAC,UAAU,WAAW,UAAU,CAAC,YAAY,CAAC,WAAW,UAAU,OAAO;AAAA,IACnJ;AAAA,IACA,OAAO;AAAA,EACX,CAAC;AACT;AAGA,SAAS,SAAS,IAAI,MAAM,SAAS,GAAG,MAAM,SAAS,GAAG;AACtD,MAAIJ,SAAQ,GAAG,QAAQ,MAAM,YAAY,IAAI,UAAU,IAAI,MAAM;AACjE,MAAI,YAAY,MAAM;AAClB,SAAK,KAAK,GAAG,IAAI,aAAa,SAAS,WAAW,SAAS,SAAS,GAAG,OAAO,YAAY,KAAK,MAAM,WAAW,OAAO,GAAG,SAAS,SAAS,CAAC,CAAC;AAAA,EAClJ;AACA,WAAS,IAAI,QAAQ,IAAI,KAAK,QAAQ,KAAK;AACvC,QAAI,OAAO,KAAK,WAAW,CAAC;AAC5B,QAAI,QAAQ,OAAiB,CAAC,KAAK;AAC/B,UAAI,CAAC,SAAS,YAAY;AACtB,QAAAA;AACJ,cAAQ;AACR,UAAI,MAAM;AACN,YAAI,YAAY;AACZ,oBAAU;AACd,aAAK,KAAK,GAAG,IAAI,kBAAkB,IAAI,QAAQ,IAAI,SAAS,CAAC,CAAC;AAAA,MAClE;AACA,kBAAY,UAAU;AAAA,IAC1B,WACS,OAAO,QAAQ,MAAM,QAAQ,GAAG;AACrC,UAAI,YAAY;AACZ,oBAAY;AAChB,gBAAU,IAAI;AAAA,IAClB;AACA,UAAM,CAAC,OAAO,QAAQ;AAAA,EAC1B;AACA,MAAI,YAAY,IAAI;AAChB,IAAAA;AACA,QAAI;AACA,gBAAU;AAAA,EAClB;AACA,SAAOA;AACX;AACA,SAAS,QAAQ,KAAK,OAAO;AACzB,WAAS,IAAI,OAAO,IAAI,IAAI,QAAQ,KAAK;AACrC,QAAI,OAAO,IAAI,WAAW,CAAC;AAC3B,QAAI,QAAQ;AACR,aAAO;AACX,QAAI,QAAQ;AACR;AAAA,EACR;AACA,SAAO;AACX;AACA,IAAM,gBAAgB;AACtB,IAAM,cAAN,MAAkB;AAAA,EACd,cAAc;AAIV,SAAK,OAAO;AAAA,EAChB;AAAA,EACA,SAAS,IAAI,MAAM,MAAM;AACrB,QAAI,KAAK,QAAQ,MAAM;AACnB,WAAK,OAAO;AACZ,UAAI;AACJ,WAAK,KAAK,QAAQ,MAAM,KAAK,QAAQ,MAAM,KAAK,QAAQ,QACpD,cAAc,KAAK,WAAW,KAAK,KAAK,MAAM,KAAK,GAAG,CAAC,GAAG;AAC1D,YAAI,WAAW,CAAC,GAAG,aAAa,SAAS,IAAI,KAAK,SAAS,GAAG,UAAU,KAAK,KAAK;AAClF,YAAI,cAAc,SAAS,IAAI,UAAU,KAAK,GAAG;AAC7C,eAAK,OAAO;AAAA,YAAC,GAAG,IAAI,eAAe,KAAK,OAAO,KAAK,QAAQ,KAAK,QAAQ,QAAQ,QAAQ;AAAA,YACrF,GAAG,IAAI,kBAAkB,GAAG,YAAY,KAAK,KAAK,GAAG,YAAY,KAAK,KAAK,MAAM;AAAA,UAAC;AAAA,MAC9F;AAAA,IACJ,WACS,KAAK,MAAM;AAChB,UAAI,UAAU,CAAC;AACf,eAAS,IAAI,KAAK,MAAM,KAAK,KAAK,SAAS,GAAG,SAAS;AACvD,WAAK,KAAK,KAAK,GAAG,IAAI,YAAY,GAAG,YAAY,KAAK,KAAK,GAAG,YAAY,KAAK,KAAK,QAAQ,OAAO,CAAC;AAAA,IACxG;AACA,WAAO;AAAA,EACX;AAAA,EACA,OAAO,IAAI,MAAM;AACb,QAAI,CAAC,KAAK;AACN,aAAO;AACX,OAAG,eAAe,MAAM,GAAG,IAAI,SAAS,KAAK,OAAO,KAAK,QAAQ,KAAK,QAAQ,QAAQ,KAAK,IAAI,CAAC;AAChG,WAAO;AAAA,EACX;AACJ;AAYA,IAAM,QAAQ;AAAA,EACV,aAAa;AAAA,IACT,EAAE,MAAM,SAAS,OAAO,KAAK;AAAA,IAC7B,EAAE,MAAM,eAAe,OAAO,EAAE,mBAAmB,KAAK,QAAQ,EAAE;AAAA,IAClE;AAAA,IACA,EAAE,MAAM,aAAa,OAAO,KAAK,QAAQ;AAAA,IACzC,EAAE,MAAM,kBAAkB,OAAO,KAAK,sBAAsB;AAAA,EAChE;AAAA,EACA,YAAY,CAAC;AAAA,IACL,MAAM;AAAA,IACN,KAAK,GAAG,MAAM;AAAE,aAAO,QAAQ,KAAK,SAAS,CAAC,IAAI,IAAI,gBAAc;AAAA,IAAM;AAAA,IAC1E,QAAQ,IAAI,MAAM,MAAM;AACpB,UAAI,KAAK,QAAQ,KAAK,OAAK,aAAa,WAAW,KAAK,CAAC,QAAQ,KAAK,MAAM,KAAK,OAAO;AACpF,eAAO;AACX,UAAI,OAAO,GAAG,SAAS;AACvB,aAAO,cAAc,KAAK,IAAI,KAAK,SAAS,IAAI,KAAK,MAAM,KAAK,OAAO,KAAK,SAAS,IAAI,MAAM,KAAK,OAAO;AAAA,IAC/G;AAAA,IACA,QAAQ;AAAA,EACZ,CAAC;AACT;AACA,IAAM,aAAN,MAAiB;AAAA,EACb,WAAW;AAAE,WAAO;AAAA,EAAO;AAAA,EAC3B,OAAO,IAAI,MAAM;AACb,OAAG,eAAe,MAAM,GAAG,IAAI,QAAQ,KAAK,OAAO,KAAK,QAAQ,KAAK,QAAQ,QAAQ;AAAA,MACjF,GAAG,IAAI,cAAc,KAAK,OAAO,KAAK,QAAQ,CAAC;AAAA,MAC/C,GAAG,GAAG,OAAO,YAAY,KAAK,QAAQ,MAAM,CAAC,GAAG,KAAK,QAAQ,CAAC;AAAA,IAClE,CAAC,CAAC;AACF,WAAO;AAAA,EACX;AACJ;AAOA,IAAM,WAAW;AAAA,EACb,aAAa;AAAA,IACT,EAAE,MAAM,QAAQ,OAAO,MAAM,OAAO,KAAK,KAAK;AAAA,IAC9C,EAAE,MAAM,cAAc,OAAO,KAAK,KAAK;AAAA,EAC3C;AAAA,EACA,YAAY,CAAC;AAAA,IACL,MAAM;AAAA,IACN,KAAK,IAAI,MAAM;AACX,aAAO,kBAAkB,KAAK,KAAK,OAAO,KAAK,GAAG,WAAW,EAAE,QAAQ,aAAa,IAAI,eAAa;AAAA,IACzG;AAAA,IACA,OAAO;AAAA,EACX,CAAC;AACT;AACA,IAAM,aAAa;AACnB,IAAM,QAAQ;AACd,IAAM,qBAAqB;AAC3B,IAAM,UAAU;AAChB,IAAM,iBAAiB;AACvB,SAAS,MAAM,KAAK,MAAM,IAAI,IAAI;AAC9B,MAAI,SAAS;AACb,WAAS,IAAI,MAAM,IAAI,IAAI;AACvB,QAAI,IAAI,CAAC,KAAK;AACV;AACR,SAAO;AACX;AACA,SAAS,eAAe,MAAM,MAAM;AAChC,QAAM,YAAY;AAClB,MAAI,IAAI,MAAM,KAAK,IAAI;AACvB,MAAI,CAAC,KAAK,mBAAmB,KAAK,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,QAAQ,GAAG,IAAI;AACtD,WAAO;AACX,MAAI,MAAM,OAAO,EAAE,CAAC,EAAE;AACtB,aAAS;AACL,QAAI,OAAO,KAAK,MAAM,CAAC,GAAGK;AAC1B,QAAI,aAAa,KAAK,IAAI,KACtB,QAAQ,OAAO,MAAM,MAAM,MAAM,KAAK,GAAG,IAAI,MAAM,MAAM,MAAM,KAAK,GAAG;AACvE;AAAA,aACK,QAAQ,QAAQA,KAAI,6BAA6B,KAAK,KAAK,MAAM,MAAM,GAAG,CAAC;AAChF,YAAM,OAAOA,GAAE;AAAA;AAEf;AAAA,EACR;AACA,SAAO;AACX;AACA,SAAS,iBAAiB,MAAM,MAAM;AAClC,UAAQ,YAAY;AACpB,MAAI,IAAI,QAAQ,KAAK,IAAI;AACzB,MAAI,CAAC;AACD,WAAO;AACX,MAAI,OAAO,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,SAAS,CAAC;AAC/B,SAAO,QAAQ,OAAO,QAAQ,MAAM,KAAK,OAAO,EAAE,CAAC,EAAE,UAAU,QAAQ,MAAM,IAAI;AACrF;AAMA,IAAM,WAAW;AAAA,EACb,aAAa,CAAC;AAAA,IACN,MAAM;AAAA,IACN,MAAM,IAAI,MAAM,QAAQ;AACpB,UAAI,MAAM,SAAS,GAAG;AACtB,UAAI,OAAO,KAAK,KAAK,GAAG,KAAK,MAAM,CAAC,CAAC;AACjC,eAAO;AACX,iBAAW,YAAY;AACvB,UAAI,IAAI,WAAW,KAAK,GAAG,IAAI,GAAG,MAAM;AACxC,UAAI,CAAC;AACD,eAAO;AACX,UAAI,EAAE,CAAC,KAAK,EAAE,CAAC,GAAG;AACd,cAAM,eAAe,GAAG,MAAM,MAAM,EAAE,CAAC,EAAE,MAAM;AAC/C,YAAI,MAAM,MAAM,GAAG,aAAa;AAC5B,cAAI,YAAY,wBAAwB,KAAK,GAAG,KAAK,MAAM,KAAK,GAAG,CAAC;AACpE,gBAAM,MAAM,UAAU,CAAC,EAAE;AAAA,QAC7B;AAAA,MACJ,WACS,EAAE,CAAC,GAAG;AACX,cAAM,iBAAiB,GAAG,MAAM,GAAG;AAAA,MACvC,OACK;AACD,cAAM,iBAAiB,GAAG,MAAM,MAAM,EAAE,CAAC,EAAE,MAAM;AACjD,YAAI,MAAM,MAAM,EAAE,CAAC,KAAK,SAAS;AAC7B,yBAAe,YAAY;AAC3B,cAAI,eAAe,KAAK,GAAG,IAAI;AAC/B,cAAI;AACA,kBAAM,EAAE,QAAQ,EAAE,CAAC,EAAE;AAAA,QAC7B;AAAA,MACJ;AACA,UAAI,MAAM;AACN,eAAO;AACX,SAAG,WAAW,GAAG,IAAI,OAAO,QAAQ,MAAM,GAAG,MAAM,CAAC;AACpD,aAAO,MAAM,GAAG;AAAA,IACpB;AAAA,EACJ,CAAC;AACT;AAMA,IAAM,MAAM,CAAC,OAAO,UAAU,eAAe,QAAQ;AACrD,SAAS,cAAc,IAAI,MAAM,MAAM;AACnC,SAAO,CAAC,IAAI,MAAM,QAAQ;AACtB,QAAI,QAAQ,MAAM,GAAG,KAAK,MAAM,CAAC,KAAK;AAClC,aAAO;AACX,QAAI,OAAO,CAAC,GAAG,IAAI,MAAM,KAAK,MAAM,CAAC,CAAC;AACtC,aAAS,IAAI,MAAM,GAAG,IAAI,GAAG,KAAK,KAAK;AACnC,UAAIC,QAAO,GAAG,KAAK,CAAC;AACpB,UAAIA,SAAQ;AACR,eAAO,GAAG,WAAW,GAAG,IAAI,MAAM,KAAK,IAAI,GAAG,KAAK,OAAO,GAAG,IAAI,MAAM,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;AACtF,UAAIA,SAAQ;AACR,aAAK,KAAK,GAAG,IAAI,UAAU,GAAG,MAAM,CAAC,CAAC;AAC1C,UAAI,MAAMA,KAAI;AACV;AAAA,IACR;AACA,WAAO;AAAA,EACX;AACJ;AAMA,IAAM,cAAc;AAAA,EAChB,aAAa;AAAA,IACT,EAAE,MAAM,eAAe,OAAO,KAAK,QAAQ,KAAK,OAAO,EAAE;AAAA,IACzD,EAAE,MAAM,mBAAmB,OAAO,KAAK,sBAAsB;AAAA,EACjE;AAAA,EACA,aAAa,CAAC;AAAA,IACN,MAAM;AAAA,IACN,OAAO,cAAc,IAAc,eAAe,iBAAiB;AAAA,EACvE,CAAC;AACT;AAMA,IAAM,YAAY;AAAA,EACd,aAAa;AAAA,IACT,EAAE,MAAM,aAAa,OAAO,KAAK,QAAQ,KAAK,OAAO,EAAE;AAAA,IACvD,EAAE,MAAM,iBAAiB,OAAO,KAAK,sBAAsB;AAAA,EAC/D;AAAA,EACA,aAAa,CAAC;AAAA,IACN,MAAM;AAAA,IACN,OAAO,cAAc,KAAe,aAAa,eAAe;AAAA,EACpE,CAAC;AACT;AAKA,IAAM,QAAQ;AAAA,EACV,aAAa,CAAC,EAAE,MAAM,SAAS,OAAO,KAAK,UAAU,CAAC;AAAA,EACtD,aAAa,CAAC;AAAA,IACN,MAAM;AAAA,IACN,MAAM,IAAI,MAAM,KAAK;AACjB,UAAI;AACJ,UAAI,QAAQ,MAAgB,EAAE,QAAQ,kBAAkB,KAAK,GAAG,MAAM,MAAM,GAAG,GAAG,GAAG,CAAC;AAClF,eAAO;AACX,aAAO,GAAG,WAAW,GAAG,IAAI,SAAS,KAAK,MAAM,IAAI,MAAM,CAAC,EAAE,MAAM,CAAC;AAAA,IACxE;AAAA,EACJ,CAAC;AACT;;;ACtvEA,IAAM,OAAoB,oBAAoB,EAAE,eAAe,EAAE,OAAO,EAAE,MAAM,QAAQ,OAAO,MAAM,EAAE,EAAE,CAAC;AAC1G,IAAM,cAA2B,IAAI,SAAS;AAC9C,IAAM,aAA0B,OAAO,UAAU;AAAA,EAC7C,OAAO;AAAA,IACU,aAAa,IAAI,UAAQ;AAClC,aAAO,CAAC,KAAK,GAAG,OAAO,KAAK,KAAK,GAAG,UAAU,KAAK,UAAU,IAAI,KAAK,QAAQ,OAAO,IAAI,IAAI,SACvF,CAAC,MAAM,WAAW,EAAE,MAAM,MAAM,IAAI,OAAO,KAAK,IAAI,EAAE,IAAI,IAAI,KAAK,GAAG;AAAA,IAChF,CAAC;AAAA,IACY,YAAY,IAAI,SAAS;AAAA,IACzB,eAAe,IAAI;AAAA,MAC5B,UAAU,MAAM;AAAA,IACpB,CAAC;AAAA,IACY,iBAAiB,IAAI;AAAA,MAC9B,UAAU;AAAA,IACd,CAAC;AAAA,EACL;AACJ,CAAC;AACD,SAAS,UAAU,MAAM;AACrB,MAAI,QAAQ,8BAA8B,KAAK,KAAK,IAAI;AACxD,SAAO,QAAQ,CAAC,MAAM,CAAC,IAAI;AAC/B;AACA,SAAS,OAAO,MAAM;AAClB,SAAO,KAAK,QAAQ,iBAAiB,KAAK,QAAQ;AACtD;AACA,SAAS,eAAe,YAAY,OAAO;AACvC,MAAI,OAAO;AACX,aAAS;AACL,QAAI,OAAO,KAAK,aAAa;AAC7B,QAAI,CAAC,SAAS,UAAU,UAAU,KAAK,IAAI,MAAM,QAAQ,WAAW;AAChE;AACJ,WAAO;AAAA,EACX;AACA,SAAO,KAAK;AAChB;AACA,IAAM,eAA4B,YAAY,GAAG,CAAC,OAAO,OAAO,QAAQ;AACpE,WAAS,OAAO,WAAW,KAAK,EAAE,aAAa,KAAK,EAAE,GAAG,MAAM,OAAO,KAAK,QAAQ;AAC/E,QAAI,KAAK,OAAO;AACZ;AACJ,QAAI,UAAU,KAAK,KAAK,KAAK,WAAW;AACxC,QAAI,WAAW;AACX;AACJ,QAAI,OAAO,eAAe,MAAM,OAAO;AACvC,QAAI,OAAO;AACP,aAAO,EAAE,MAAM,KAAK,IAAI,KAAK;AAAA,EACrC;AACA,SAAO;AACX,CAAC;AACD,SAAS,OAAOC,SAAQ;AACpB,SAAO,IAAI,SAAS,MAAMA,SAAQ,CAAC,YAAY,GAAG,UAAU;AAChE;AAIA,IAAM,qBAAkC,OAAO,UAAU;AACzD,IAAM,WAAwB,WAAW,UAAU,CAAC,KAAK,WAAW,aAAa,OAAO;AAAA,EAChF,OAAO;AAAA,IACU,aAAa,IAAI;AAAA,MAC1B,OAAO,CAAC,MAAM,WAAW,EAAE,MAAM,MAAM,IAAI,OAAO,KAAK,IAAI,EAAE,IAAI,IAAI,KAAK,GAAG;AAAA,IACjF,CAAC;AAAA,EACL;AACJ,CAAC,CAAC;AAKN,IAAM,mBAAgC,OAAO,QAAQ;AACrD,SAAS,cAAc,WAAW,iBAAiB;AAC/C,SAAO,CAAC,SAAS;AACb,QAAI,QAAQ,WAAW;AACnB,UAAI,QAAQ;AAEZ,aAAO,MAAM,KAAK,IAAI,EAAE,CAAC;AACzB,UAAI,OAAO,aAAa;AACpB,gBAAQ,UAAU,IAAI;AAAA;AAEtB,gBAAQ,oBAAoB,kBAAkB,WAAW,MAAM,IAAI;AACvE,UAAI,iBAAiB;AACjB,eAAO,MAAM,UAAU,MAAM,QAAQ,SAAS,SAAS,aAAa,kBAAkB,MAAM,KAAK,CAAC;AAAA,eAC7F;AACL,eAAO,MAAM;AAAA,IACrB;AACA,WAAO,kBAAkB,gBAAgB,SAAS;AAAA,EACtD;AACJ;AAEA,IAAM,UAAN,MAAc;AAAA,EACV,YAAY,MAAM,MAAM,IAAI,aAAa,YAAY,MAAM,MAAM;AAC7D,SAAK,OAAO;AACZ,SAAK,OAAO;AACZ,SAAK,KAAK;AACV,SAAK,cAAc;AACnB,SAAK,aAAa;AAClB,SAAK,OAAO;AACZ,SAAK,OAAO;AAAA,EAChB;AAAA,EACA,MAAM,UAAU,WAAW,MAAM;AAC7B,QAAI,SAAS,KAAK,eAAe,KAAK,KAAK,QAAQ,eAAe,MAAM;AACxE,QAAI,YAAY,MAAM;AAClB,aAAO,OAAO,SAAS;AACnB,kBAAU;AACd,aAAO;AAAA,IACX,OACK;AACD,eAAS,IAAI,KAAK,KAAK,KAAK,OAAO,OAAO,SAAS,KAAK,WAAW,QAAQ,IAAI,GAAG;AAC9E,kBAAU;AACd,aAAO,UAAU,WAAW,KAAK,aAAa;AAAA,IAClD;AAAA,EACJ;AAAA,EACA,OAAO,KAAK,KAAK;AACb,QAAI,SAAS,KAAK,KAAK,QAAQ,gBAAgB,OAAQ,CAAC,WAAW,KAAK,MAAM,GAAG,EAAE,CAAC,IAAI,GAAI,IAAI;AAChG,WAAO,KAAK,cAAc,SAAS,KAAK,OAAO,KAAK;AAAA,EACxD;AACJ;AACA,SAAS,WAAW,MAAM,KAAK;AAC3B,MAAI,QAAQ,CAAC,GAAG,UAAU,CAAC;AAC3B,WAAS,MAAM,MAAM,KAAK,MAAM,IAAI,QAAQ;AACxC,QAAI,IAAI,QAAQ;AACZ,aAAO;AACX,QAAI,IAAI,QAAQ,cAAc,IAAI,QAAQ;AACtC,YAAM,KAAK,GAAG;AAAA,EACtB;AACA,WAAS,IAAI,MAAM,SAAS,GAAG,KAAK,GAAG,KAAK;AACxC,QAAIC,QAAO,MAAM,CAAC,GAAG;AACrB,QAAI,OAAO,IAAI,OAAOA,MAAK,IAAI,GAAG,WAAWA,MAAK,OAAO,KAAK;AAC9D,QAAIA,MAAK,QAAQ,iBAAiB,QAAQ,WAAW,KAAK,KAAK,KAAK,MAAM,QAAQ,CAAC,IAAI;AACnF,cAAQ,KAAK,IAAI,QAAQA,OAAM,UAAU,WAAW,MAAM,CAAC,EAAE,QAAQ,IAAI,MAAM,CAAC,GAAG,KAAK,IAAI,CAAC;AAAA,IACjG,WACSA,MAAK,QAAQ,cAAcA,MAAK,OAAO,QAAQ,kBACnD,QAAQ,qBAAqB,KAAK,KAAK,KAAK,MAAM,QAAQ,CAAC,IAAI;AAChE,UAAI,QAAQ,MAAM,CAAC,GAAG,MAAM,MAAM,CAAC,EAAE;AACrC,UAAI,MAAM,UAAU,GAAG;AACnB,gBAAQ,MAAM,MAAM,GAAG,MAAM,SAAS,CAAC;AACvC,eAAO;AAAA,MACX;AACA,cAAQ,KAAK,IAAI,QAAQA,MAAK,QAAQ,UAAU,WAAW,KAAK,MAAM,CAAC,GAAG,OAAO,MAAM,CAAC,GAAGA,KAAI,CAAC;AAAA,IACpG,WACSA,MAAK,QAAQ,cAAcA,MAAK,OAAO,QAAQ,iBACnD,QAAQ,qCAAqC,KAAK,KAAK,KAAK,MAAM,QAAQ,CAAC,IAAI;AAChF,UAAI,QAAQ,MAAM,CAAC,GAAG,MAAM,MAAM,CAAC,EAAE;AACrC,UAAI,MAAM,SAAS,GAAG;AAClB,gBAAQ,MAAM,MAAM,GAAG,MAAM,SAAS,CAAC;AACvC,eAAO;AAAA,MACX;AACA,UAAI,OAAO,MAAM,CAAC;AAClB,UAAI,MAAM,CAAC;AACP,gBAAQ,MAAM,CAAC,EAAE,QAAQ,QAAQ,GAAG;AACxC,cAAQ,KAAK,IAAI,QAAQA,MAAK,QAAQ,UAAU,WAAW,KAAK,MAAM,CAAC,GAAG,OAAO,MAAMA,KAAI,CAAC;AAAA,IAChG;AAAA,EACJ;AACA,SAAO;AACX;AACA,SAAS,WAAW,MAAM,KAAK;AAC3B,SAAO,sBAAsB,KAAK,IAAI,YAAY,KAAK,MAAM,KAAK,OAAO,EAAE,CAAC;AAChF;AACA,SAAS,aAAa,OAAO,KAAK,SAAS,SAAS,GAAG;AACnD,WAAS,OAAO,IAAI,OAAO,WAAS;AAChC,QAAI,KAAK,QAAQ,YAAY;AACzB,UAAI,IAAI,WAAW,MAAM,GAAG;AAC5B,UAAI,SAAS,CAAC,EAAE,CAAC;AACjB,UAAI,QAAQ,GAAG;AACX,YAAI,UAAU,OAAO;AACjB;AACJ,gBAAQ,KAAK,EAAE,MAAM,KAAK,OAAO,EAAE,CAAC,EAAE,QAAQ,IAAI,KAAK,OAAO,EAAE,CAAC,EAAE,QAAQ,QAAQ,OAAO,OAAO,IAAI,MAAM,EAAE,CAAC;AAAA,MAClH;AACA,aAAO;AAAA,IACX;AACA,QAAI,OAAO,KAAK;AAChB,QAAI,CAAC;AACD;AACJ,WAAO;AAAA,EACX;AACJ;AACA,SAAS,gBAAgB,SAAS,OAAO;AACrC,MAAI,QAAQ,UAAU,KAAK,OAAO,EAAE,CAAC,EAAE;AACvC,MAAI,CAAC,SAAS,MAAM,MAAM,UAAU,KAAK;AACrC,WAAO;AACX,MAAI,MAAM,YAAY,SAAS,GAAG,KAAK;AACvC,MAAIC,SAAQ;AACZ,WAAS,IAAI,KAAK,IAAI,KAAI;AACtB,QAAI,KAAK,GAAG;AACR,MAAAA,UAAS;AACT,WAAK;AAAA,IACT,OACK;AACD,MAAAA,UAAS;AACT;AAAA,IACJ;AAAA,EACJ;AACA,SAAOA,SAAQ,QAAQ,MAAM,KAAK;AACtC;AAYA,IAAM,8BAA8B,CAAC,EAAE,OAAO,SAAS,MAAM;AACzD,MAAI,OAAO,WAAW,KAAK,GAAG,EAAE,IAAI,IAAI;AACxC,MAAI,OAAO,MAAM,UAAU,MAAM,cAAc,WAAS;AACpD,QAAI,CAAC,MAAM,SAAS,CAAC,iBAAiB,WAAW,OAAO,MAAM,MAAM,EAAE,KAAK,CAAC,iBAAiB,WAAW,OAAO,MAAM,MAAM,CAAC;AACxH,aAAO,OAAO,EAAE,MAAM;AAC1B,QAAI,MAAM,MAAM,MAAM,OAAO,IAAI,OAAO,GAAG;AAC3C,QAAI,UAAU,WAAW,KAAK,aAAa,KAAK,EAAE,GAAG,GAAG;AACxD,WAAO,QAAQ,UAAU,QAAQ,QAAQ,SAAS,CAAC,EAAE,OAAO,MAAM,KAAK;AACnE,cAAQ,IAAI;AAChB,QAAI,CAAC,QAAQ;AACT,aAAO,OAAO,EAAE,MAAM;AAC1B,QAAI,QAAQ,QAAQ,QAAQ,SAAS,CAAC;AACtC,QAAI,MAAM,KAAK,MAAM,WAAW,SAAS,MAAM,KAAK;AAChD,aAAO,OAAO,EAAE,MAAM;AAC1B,QAAI,YAAY,OAAQ,MAAM,KAAK,MAAM,WAAW,UAAW,CAAC,KAAK,KAAK,KAAK,KAAK,MAAM,MAAM,EAAE,CAAC;AAEnG,QAAI,MAAM,QAAQ,WAAW;AACzB,UAAI,QAAQ,MAAM,KAAK,YAAY,SAAS,MAAM,KAAK,SAAS,YAAY,UAAU;AAEtF,UAAI,MAAM,MAAM,OAAO,UAAU,OAAO,KAAK,OACzC,KAAK,OAAO,KAAK,CAAC,SAAS,KAAK,IAAI,OAAO,KAAK,OAAO,CAAC,EAAE,IAAI,GAAG;AACjE,YAAI,OAAO,QAAQ,SAAS,IAAI,QAAQ,QAAQ,SAAS,CAAC,IAAI;AAC9D,YAAI,OAAOC,UAAS;AACpB,YAAI,QAAQ,KAAK,MAAM;AACnB,kBAAQ,KAAK,OAAO,KAAK;AACzB,UAAAA,UAAS,KAAK,OAAO,KAAK,CAAC;AAAA,QAC/B,OACK;AACD,kBAAQ,KAAK,QAAQ,OAAO,KAAK,KAAK;AAAA,QAC1C;AACA,YAAIC,WAAU,CAAC,EAAE,MAAM,OAAO,IAAI,KAAK,QAAAD,QAAO,CAAC;AAC/C,YAAI,MAAM,KAAK,QAAQ;AACnB,uBAAa,MAAM,MAAM,KAAKC,UAAS,EAAE;AAC7C,YAAI,QAAQ,KAAK,KAAK,QAAQ;AAC1B,uBAAa,KAAK,MAAM,KAAKA,QAAO;AACxC,eAAO,EAAE,OAAO,gBAAgB,OAAO,QAAQD,QAAO,MAAM,GAAG,SAAAC,SAAQ;AAAA,MAC3E,OACK;AACD,YAAID,UAAS,UAAU,SAAS,OAAO,IAAI;AAC3C,eAAO;AAAA,UAAE,OAAO,gBAAgB,OAAO,MAAMA,QAAO,SAAS,CAAC;AAAA,UAC1D,SAAS,EAAE,MAAM,KAAK,MAAM,QAAQA,UAAS,MAAM,UAAU;AAAA,QAAE;AAAA,MACvE;AAAA,IACJ;AACA,QAAI,MAAM,KAAK,QAAQ,gBAAgB,aAAa,KAAK,MAAM;AAC3D,UAAI,WAAW,IAAI,OAAO,KAAK,OAAO,CAAC,GAAG,SAAS,QAAQ,KAAK,SAAS,IAAI;AAE7E,UAAI,UAAU,OAAO,SAAS,MAAM,MAAM;AACtC,YAAIC,WAAU,MAAM,QAAQ;AAAA,UAAC,EAAE,MAAM,SAAS,OAAO,OAAO,OAAO,IAAI,SAAS,GAAG;AAAA,UAC/E,EAAE,MAAM,KAAK,OAAO,MAAM,MAAM,IAAI,KAAK,GAAG;AAAA,QAAC,CAAC;AAClD,eAAO,EAAE,OAAO,MAAM,IAAIA,QAAO,GAAG,SAAAA,SAAQ;AAAA,MAChD;AAAA,IACJ;AACA,QAAIA,WAAU,CAAC;AACf,QAAI,MAAM,KAAK,QAAQ;AACnB,mBAAa,MAAM,MAAM,KAAKA,QAAO;AACzC,QAAI,YAAY,MAAM,QAAQ,MAAM,KAAK,OAAO,KAAK;AACrD,QAAI,SAAS;AAEb,QAAI,CAAC,aAAa,kBAAkB,KAAK,KAAK,IAAI,EAAE,CAAC,EAAE,UAAU,MAAM,IAAI;AACvE,eAAS,IAAI,GAAG,IAAI,QAAQ,SAAS,GAAG,KAAK,GAAG,KAAK;AACjD,kBAAU,KAAK,KAAK,CAAC,YAAY,QAAQ,CAAC,EAAE,OAAO,KAAK,CAAC,IACnD,QAAQ,CAAC,EAAE,MAAM,IAAI,IAAI,YAAY,KAAK,MAAM,GAAG,QAAQ,IAAI,CAAC,EAAE,IAAI,IAAI,OAAO,SAAS,IAAI;AAAA,MACxG;AAAA,IACJ;AACA,QAAI,OAAO;AACX,WAAO,OAAO,KAAK,QAAQ,KAAK,KAAK,KAAK,KAAK,OAAO,OAAO,KAAK,OAAO,CAAC,CAAC;AACvE;AACJ,aAAS,gBAAgB,QAAQ,KAAK;AACtC,QAAI,aAAa,MAAM,MAAM,MAAM,GAAG;AAClC,eAAS,UAAU,SAAS,OAAO,IAAI,IAAI,MAAM,YAAY;AACjE,IAAAA,SAAQ,KAAK,EAAE,MAAM,IAAI,KAAK,QAAQ,MAAM,YAAY,OAAO,CAAC;AAChE,WAAO,EAAE,OAAO,gBAAgB,OAAO,OAAO,OAAO,SAAS,CAAC,GAAG,SAAAA,SAAQ;AAAA,EAC9E,CAAC;AACD,MAAI;AACA,WAAO;AACX,WAAS,MAAM,OAAO,SAAS,EAAE,gBAAgB,MAAM,WAAW,QAAQ,CAAC,CAAC;AAC5E,SAAO;AACX;AACA,SAAS,OAAO,MAAM;AAClB,SAAO,KAAK,QAAQ,eAAe,KAAK,QAAQ;AACpD;AACA,SAAS,aAAa,MAAM,KAAK;AAC7B,MAAI,KAAK,QAAQ,iBAAiB,KAAK,QAAQ;AAC3C,WAAO;AACX,MAAI,QAAQ,KAAK,YAAY,SAAS,KAAK,SAAS,YAAY,UAAU;AAC1E,MAAI,CAAC;AACD,WAAO;AACX,MAAI,QAAQ,IAAI,OAAO,MAAM,EAAE,GAAG,QAAQ,IAAI,OAAO,OAAO,IAAI;AAChE,MAAI,QAAQ,WAAW,KAAK,MAAM,IAAI;AACtC,SAAO,MAAM,UAAU,QAAQ,IAAI,KAAK,MAAM;AAClD;AACA,SAAS,UAAU,SAAS,OAAO,MAAM;AACrC,MAAI,SAAS;AACb,WAAS,IAAI,GAAG,IAAI,QAAQ,SAAS,GAAG,KAAK,GAAG,KAAK;AACjD,cAAU,QAAQ,CAAC,EAAE,MAAM,IAAI,IACzB,YAAY,KAAK,MAAM,GAAG,QAAQ,IAAI,CAAC,EAAE,IAAI,IAAI,OAAO,SACxD,MAAM,IAAI,CAAC;AAAA,EACrB;AACA,SAAO,gBAAgB,QAAQ,KAAK;AACxC;AACA,SAAS,qBAAqB,MAAM,KAAK;AACrC,MAAI,OAAO,KAAK,aAAa,KAAK,EAAE,GAAG,OAAO;AAC9C,MAAI,OAAO,IAAI,GAAG;AACd,WAAO,KAAK;AACZ,WAAO,KAAK;AAAA,EAChB;AACA,WAAS,MAAM,OAAO,KAAK,YAAY,IAAI,KAAI;AAC3C,QAAI,OAAO,IAAI,GAAG;AACd,aAAO,KAAK;AAAA,IAChB,WACS,KAAK,QAAQ,iBAAiB,KAAK,QAAQ,cAAc;AAC9D,aAAO,KAAK;AACZ,aAAO,KAAK;AAAA,IAChB,OACK;AACD;AAAA,IACJ;AAAA,EACJ;AACA,SAAO;AACX;AAYA,IAAM,uBAAuB,CAAC,EAAE,OAAO,SAAS,MAAM;AAClD,MAAI,OAAO,WAAW,KAAK;AAC3B,MAAI,OAAO,MAAM,UAAU,MAAM,cAAc,WAAS;AACpD,QAAI,MAAM,MAAM,MAAM,EAAE,IAAI,IAAI;AAChC,QAAI,MAAM,SAAS,iBAAiB,WAAW,OAAO,MAAM,IAAI,GAAG;AAC/D,UAAI,OAAO,IAAI,OAAO,GAAG;AACzB,UAAI,UAAU,WAAW,qBAAqB,MAAM,GAAG,GAAG,GAAG;AAC7D,UAAI,QAAQ,QAAQ;AAChB,YAAI,QAAQ,QAAQ,QAAQ,SAAS,CAAC;AACtC,YAAI,WAAW,MAAM,KAAK,MAAM,WAAW,UAAU,MAAM,aAAa,IAAI;AAE5E,YAAI,MAAM,KAAK,OAAO,YAAY,CAAC,KAAK,KAAK,KAAK,KAAK,MAAM,UAAU,MAAM,KAAK,IAAI,CAAC;AACnF,iBAAO;AAAA,YAAE,OAAO,gBAAgB,OAAO,KAAK,OAAO,QAAQ;AAAA,YACvD,SAAS,EAAE,MAAM,KAAK,OAAO,UAAU,IAAI,IAAI;AAAA,UAAE;AACzD,YAAI,MAAM,KAAK,QAAQ;AAAA;AAAA;AAAA,SAIlB,CAAC,MAAM,QAAQ,KAAK,QAAQ,MAAM,KAAK,QAAQ,CAAC,KAAK,KAAK,KAAK,KAAK,MAAM,GAAG,MAAM,EAAE,CAAC,IAAI;AAC3F,cAAI,QAAQ,KAAK,OAAO,MAAM;AAE9B,cAAI,MAAM,QAAQ,MAAM,KAAK,OAAO,MAAM,KAAK,QAAQ,KAAK,KAAK,KAAK,KAAK,MAAM,MAAM,MAAM,MAAM,EAAE,CAAC,GAAG;AACrG,gBAAI,SAAS,MAAM,MAAM,YAAY,KAAK,MAAM,GAAG,MAAM,EAAE,IAAI,YAAY,KAAK,MAAM,GAAG,MAAM,IAAI,CAAC;AACpG,gBAAI,SAAS,KAAK;AACd,uBAAS,gBAAgB,QAAQ,KAAK;AAC1C,mBAAO;AAAA,cAAE,OAAO,gBAAgB,OAAO,QAAQ,OAAO,MAAM;AAAA,cACxD,SAAS,EAAE,MAAM,OAAO,IAAI,KAAK,OAAO,MAAM,IAAI,OAAO;AAAA,YAAE;AAAA,UACnE;AAEA,cAAI,QAAQ;AACR,mBAAO,EAAE,OAAO,gBAAgB,OAAO,KAAK,GAAG,SAAS,EAAE,MAAM,OAAO,IAAI,IAAI,EAAE;AAAA,QACzF;AAAA,MACJ;AAAA,IACJ;AACA,WAAO,OAAO,EAAE,MAAM;AAAA,EAC1B,CAAC;AACD,MAAI;AACA,WAAO;AACX,WAAS,MAAM,OAAO,SAAS,EAAE,gBAAgB,MAAM,WAAW,SAAS,CAAC,CAAC;AAC7E,SAAO;AACX;AAQA,IAAM,iBAAiB;AAAA,EACnB,EAAE,KAAK,SAAS,KAAK,4BAA4B;AAAA,EACjD,EAAE,KAAK,aAAa,KAAK,qBAAqB;AAClD;AACA,IAAM,cAA2B,KAAK,EAAE,kBAAkB,MAAM,CAAC;AAIjE,SAAS,SAAS,SAAS,CAAC,GAAG;AAC3B,MAAI,EAAE,eAAe,qBAAqB,YAAY,MAAM,MAAM,EAAE,QAAAJ,QAAO,IAAI,oBAAoB,mBAAmB,MAAM,kBAAkB,YAAY,IAAI;AAC9J,MAAI,EAAEA,mBAAkB;AACpB,UAAM,IAAI,WAAW,gEAAgE;AACzF,MAAI,aAAa,OAAO,aAAa,CAAC,OAAO,UAAU,IAAI,CAAC;AAC5D,MAAI,UAAU,CAAC,gBAAgB,OAAO,GAAG;AACzC,MAAI,+BAA+B,iBAAiB;AAChD,YAAQ,KAAK,oBAAoB,OAAO;AACxC,kBAAc,oBAAoB;AAAA,EACtC,WACS,qBAAqB;AAC1B,kBAAc;AAAA,EAClB;AACA,MAAI,aAAa,iBAAiB,cAAc,cAAc,eAAe,WAAW,IAAI;AAC5F,aAAW,KAAK,UAAU,EAAE,YAAY,YAAY,gBAAgB,SAAS,OAAO,CAAC,CAAC;AACtF,MAAI;AACA,YAAQ,KAAK,KAAK,KAAK,OAAO,GAAG,cAAc,CAAC,CAAC;AACrD,MAAI,OAAO,OAAOA,QAAO,UAAU,UAAU,CAAC;AAC9C,MAAI;AACA,YAAQ,KAAK,KAAK,KAAK,GAAG,EAAE,cAAc,kBAAkB,CAAC,CAAC;AAClE,SAAO,IAAI,gBAAgB,MAAM,OAAO;AAC5C;AACA,SAAS,kBAAkB,SAAS;AAChC,MAAI,EAAE,OAAO,IAAI,IAAI,SAAS,IAAI,4BAA4B,KAAK,MAAM,SAAS,MAAM,IAAI,GAAG,CAAC;AAChG,MAAI,CAAC;AACD,WAAO;AACX,MAAI,OAAO,WAAW,KAAK,EAAE,aAAa,KAAK,EAAE;AACjD,SAAO,QAAQ,CAAC,KAAK,KAAK,OAAO;AAC7B,QAAI,KAAK,QAAQ,eAAe,KAAK,QAAQ,gBAAgB,KAAK,QAAQ,gCACtE,KAAK,QAAQ,kBAAkB,KAAK,QAAQ,UAAU,KAAK,QAAQ;AACnE,aAAO;AACX,WAAO,KAAK;AAAA,EAChB;AACA,SAAO;AAAA,IACH,MAAM,MAAM,EAAE,CAAC,EAAE;AAAA,IAAQ,IAAI;AAAA,IAC7B,SAAS,mBAAmB;AAAA,IAC5B,UAAU;AAAA,EACd;AACJ;AACA,IAAI,kBAAkB;AACtB,SAAS,qBAAqB;AAC1B,MAAI;AACA,WAAO;AACX,MAAI,SAAS,qBAAqB,IAAI,kBAAkB,YAAY,OAAO,EAAE,YAAY,YAAY,CAAC,GAAG,GAAG,IAAI,CAAC;AACjH,SAAO,kBAAkB,SAAS,OAAO,UAAU,CAAC;AACxD;", "names": ["Type", "elt", "count", "parser", "nodeTypes", "spec", "node", "m", "next", "parser", "node", "space", "insert", "changes"]}