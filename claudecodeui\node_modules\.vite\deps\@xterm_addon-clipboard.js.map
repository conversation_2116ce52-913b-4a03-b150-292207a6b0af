{"version": 3, "sources": ["webpack://ClipboardAddon/webpack/universalModuleDefinition", "webpack://ClipboardAddon/node_modules/js-base64/base64.js", "webpack://ClipboardAddon/webpack/bootstrap", "webpack://ClipboardAddon/webpack/runtime/global", "webpack://ClipboardAddon/src/ClipboardAddon.ts"], "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([], factory);\n\telse if(typeof exports === 'object')\n\t\texports[\"ClipboardAddon\"] = factory();\n\telse\n\t\troot[\"ClipboardAddon\"] = factory();\n})(self, () => {\nreturn ", "//\n// THIS FILE IS AUTOMATICALLY GENERATED! DO NOT EDIT BY HAND!\n//\n;\n(function (global, factory) {\n    typeof exports === 'object' && typeof module !== 'undefined'\n        ? module.exports = factory()\n        : typeof define === 'function' && define.amd\n            ? define(factory) :\n            // cf. https://github.com/dankogai/js-base64/issues/119\n            (function () {\n                // existing version for noConflict()\n                var _Base64 = global.Base64;\n                var gBase64 = factory();\n                gBase64.noConflict = function () {\n                    global.Base64 = _Base64;\n                    return gBase64;\n                };\n                if (global.Meteor) { // Meteor.js\n                    Base64 = gBase64;\n                }\n                global.Base64 = gBase64;\n            })();\n}((typeof self !== 'undefined' ? self\n    : typeof window !== 'undefined' ? window\n        : typeof global !== 'undefined' ? global\n            : this), function () {\n    'use strict';\n    /**\n     *  base64.ts\n     *\n     *  Licensed under the BSD 3-Clause License.\n     *    http://opensource.org/licenses/BSD-3-Clause\n     *\n     *  References:\n     *    http://en.wikipedia.org/wiki/Base64\n     *\n     * <AUTHOR> (https://github.com/dankogai)\n     */\n    var version = '3.7.7';\n    /**\n     * @deprecated use lowercase `version`.\n     */\n    var VERSION = version;\n    var _hasBuffer = typeof Buffer === 'function';\n    var _TD = typeof TextDecoder === 'function' ? new TextDecoder() : undefined;\n    var _TE = typeof TextEncoder === 'function' ? new TextEncoder() : undefined;\n    var b64ch = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=';\n    var b64chs = Array.prototype.slice.call(b64ch);\n    var b64tab = (function (a) {\n        var tab = {};\n        a.forEach(function (c, i) { return tab[c] = i; });\n        return tab;\n    })(b64chs);\n    var b64re = /^(?:[A-Za-z\\d+\\/]{4})*?(?:[A-Za-z\\d+\\/]{2}(?:==)?|[A-Za-z\\d+\\/]{3}=?)?$/;\n    var _fromCC = String.fromCharCode.bind(String);\n    var _U8Afrom = typeof Uint8Array.from === 'function'\n        ? Uint8Array.from.bind(Uint8Array)\n        : function (it) { return new Uint8Array(Array.prototype.slice.call(it, 0)); };\n    var _mkUriSafe = function (src) { return src\n        .replace(/=/g, '').replace(/[+\\/]/g, function (m0) { return m0 == '+' ? '-' : '_'; }); };\n    var _tidyB64 = function (s) { return s.replace(/[^A-Za-z0-9\\+\\/]/g, ''); };\n    /**\n     * polyfill version of `btoa`\n     */\n    var btoaPolyfill = function (bin) {\n        // console.log('polyfilled');\n        var u32, c0, c1, c2, asc = '';\n        var pad = bin.length % 3;\n        for (var i = 0; i < bin.length;) {\n            if ((c0 = bin.charCodeAt(i++)) > 255 ||\n                (c1 = bin.charCodeAt(i++)) > 255 ||\n                (c2 = bin.charCodeAt(i++)) > 255)\n                throw new TypeError('invalid character found');\n            u32 = (c0 << 16) | (c1 << 8) | c2;\n            asc += b64chs[u32 >> 18 & 63]\n                + b64chs[u32 >> 12 & 63]\n                + b64chs[u32 >> 6 & 63]\n                + b64chs[u32 & 63];\n        }\n        return pad ? asc.slice(0, pad - 3) + \"===\".substring(pad) : asc;\n    };\n    /**\n     * does what `window.btoa` of web browsers do.\n     * @param {String} bin binary string\n     * @returns {string} Base64-encoded string\n     */\n    var _btoa = typeof btoa === 'function' ? function (bin) { return btoa(bin); }\n        : _hasBuffer ? function (bin) { return Buffer.from(bin, 'binary').toString('base64'); }\n            : btoaPolyfill;\n    var _fromUint8Array = _hasBuffer\n        ? function (u8a) { return Buffer.from(u8a).toString('base64'); }\n        : function (u8a) {\n            // cf. https://stackoverflow.com/questions/12710001/how-to-convert-uint8-array-to-base64-encoded-string/12713326#12713326\n            var maxargs = 0x1000;\n            var strs = [];\n            for (var i = 0, l = u8a.length; i < l; i += maxargs) {\n                strs.push(_fromCC.apply(null, u8a.subarray(i, i + maxargs)));\n            }\n            return _btoa(strs.join(''));\n        };\n    /**\n     * converts a Uint8Array to a Base64 string.\n     * @param {boolean} [urlsafe] URL-and-filename-safe a la RFC4648 §5\n     * @returns {string} Base64 string\n     */\n    var fromUint8Array = function (u8a, urlsafe) {\n        if (urlsafe === void 0) { urlsafe = false; }\n        return urlsafe ? _mkUriSafe(_fromUint8Array(u8a)) : _fromUint8Array(u8a);\n    };\n    // This trick is found broken https://github.com/dankogai/js-base64/issues/130\n    // const utob = (src: string) => unescape(encodeURIComponent(src));\n    // reverting good old fationed regexp\n    var cb_utob = function (c) {\n        if (c.length < 2) {\n            var cc = c.charCodeAt(0);\n            return cc < 0x80 ? c\n                : cc < 0x800 ? (_fromCC(0xc0 | (cc >>> 6))\n                    + _fromCC(0x80 | (cc & 0x3f)))\n                    : (_fromCC(0xe0 | ((cc >>> 12) & 0x0f))\n                        + _fromCC(0x80 | ((cc >>> 6) & 0x3f))\n                        + _fromCC(0x80 | (cc & 0x3f)));\n        }\n        else {\n            var cc = 0x10000\n                + (c.charCodeAt(0) - 0xD800) * 0x400\n                + (c.charCodeAt(1) - 0xDC00);\n            return (_fromCC(0xf0 | ((cc >>> 18) & 0x07))\n                + _fromCC(0x80 | ((cc >>> 12) & 0x3f))\n                + _fromCC(0x80 | ((cc >>> 6) & 0x3f))\n                + _fromCC(0x80 | (cc & 0x3f)));\n        }\n    };\n    var re_utob = /[\\uD800-\\uDBFF][\\uDC00-\\uDFFFF]|[^\\x00-\\x7F]/g;\n    /**\n     * @deprecated should have been internal use only.\n     * @param {string} src UTF-8 string\n     * @returns {string} UTF-16 string\n     */\n    var utob = function (u) { return u.replace(re_utob, cb_utob); };\n    //\n    var _encode = _hasBuffer\n        ? function (s) { return Buffer.from(s, 'utf8').toString('base64'); }\n        : _TE\n            ? function (s) { return _fromUint8Array(_TE.encode(s)); }\n            : function (s) { return _btoa(utob(s)); };\n    /**\n     * converts a UTF-8-encoded string to a Base64 string.\n     * @param {boolean} [urlsafe] if `true` make the result URL-safe\n     * @returns {string} Base64 string\n     */\n    var encode = function (src, urlsafe) {\n        if (urlsafe === void 0) { urlsafe = false; }\n        return urlsafe\n            ? _mkUriSafe(_encode(src))\n            : _encode(src);\n    };\n    /**\n     * converts a UTF-8-encoded string to URL-safe Base64 RFC4648 §5.\n     * @returns {string} Base64 string\n     */\n    var encodeURI = function (src) { return encode(src, true); };\n    // This trick is found broken https://github.com/dankogai/js-base64/issues/130\n    // const btou = (src: string) => decodeURIComponent(escape(src));\n    // reverting good old fationed regexp\n    var re_btou = /[\\xC0-\\xDF][\\x80-\\xBF]|[\\xE0-\\xEF][\\x80-\\xBF]{2}|[\\xF0-\\xF7][\\x80-\\xBF]{3}/g;\n    var cb_btou = function (cccc) {\n        switch (cccc.length) {\n            case 4:\n                var cp = ((0x07 & cccc.charCodeAt(0)) << 18)\n                    | ((0x3f & cccc.charCodeAt(1)) << 12)\n                    | ((0x3f & cccc.charCodeAt(2)) << 6)\n                    | (0x3f & cccc.charCodeAt(3)), offset = cp - 0x10000;\n                return (_fromCC((offset >>> 10) + 0xD800)\n                    + _fromCC((offset & 0x3FF) + 0xDC00));\n            case 3:\n                return _fromCC(((0x0f & cccc.charCodeAt(0)) << 12)\n                    | ((0x3f & cccc.charCodeAt(1)) << 6)\n                    | (0x3f & cccc.charCodeAt(2)));\n            default:\n                return _fromCC(((0x1f & cccc.charCodeAt(0)) << 6)\n                    | (0x3f & cccc.charCodeAt(1)));\n        }\n    };\n    /**\n     * @deprecated should have been internal use only.\n     * @param {string} src UTF-16 string\n     * @returns {string} UTF-8 string\n     */\n    var btou = function (b) { return b.replace(re_btou, cb_btou); };\n    /**\n     * polyfill version of `atob`\n     */\n    var atobPolyfill = function (asc) {\n        // console.log('polyfilled');\n        asc = asc.replace(/\\s+/g, '');\n        if (!b64re.test(asc))\n            throw new TypeError('malformed base64.');\n        asc += '=='.slice(2 - (asc.length & 3));\n        var u24, bin = '', r1, r2;\n        for (var i = 0; i < asc.length;) {\n            u24 = b64tab[asc.charAt(i++)] << 18\n                | b64tab[asc.charAt(i++)] << 12\n                | (r1 = b64tab[asc.charAt(i++)]) << 6\n                | (r2 = b64tab[asc.charAt(i++)]);\n            bin += r1 === 64 ? _fromCC(u24 >> 16 & 255)\n                : r2 === 64 ? _fromCC(u24 >> 16 & 255, u24 >> 8 & 255)\n                    : _fromCC(u24 >> 16 & 255, u24 >> 8 & 255, u24 & 255);\n        }\n        return bin;\n    };\n    /**\n     * does what `window.atob` of web browsers do.\n     * @param {String} asc Base64-encoded string\n     * @returns {string} binary string\n     */\n    var _atob = typeof atob === 'function' ? function (asc) { return atob(_tidyB64(asc)); }\n        : _hasBuffer ? function (asc) { return Buffer.from(asc, 'base64').toString('binary'); }\n            : atobPolyfill;\n    //\n    var _toUint8Array = _hasBuffer\n        ? function (a) { return _U8Afrom(Buffer.from(a, 'base64')); }\n        : function (a) { return _U8Afrom(_atob(a).split('').map(function (c) { return c.charCodeAt(0); })); };\n    /**\n     * converts a Base64 string to a Uint8Array.\n     */\n    var toUint8Array = function (a) { return _toUint8Array(_unURI(a)); };\n    //\n    var _decode = _hasBuffer\n        ? function (a) { return Buffer.from(a, 'base64').toString('utf8'); }\n        : _TD\n            ? function (a) { return _TD.decode(_toUint8Array(a)); }\n            : function (a) { return btou(_atob(a)); };\n    var _unURI = function (a) { return _tidyB64(a.replace(/[-_]/g, function (m0) { return m0 == '-' ? '+' : '/'; })); };\n    /**\n     * converts a Base64 string to a UTF-8 string.\n     * @param {String} src Base64 string.  Both normal and URL-safe are supported\n     * @returns {string} UTF-8 string\n     */\n    var decode = function (src) { return _decode(_unURI(src)); };\n    /**\n     * check if a value is a valid Base64 string\n     * @param {String} src a value to check\n      */\n    var isValid = function (src) {\n        if (typeof src !== 'string')\n            return false;\n        var s = src.replace(/\\s+/g, '').replace(/={0,2}$/, '');\n        return !/[^\\s0-9a-zA-Z\\+/]/.test(s) || !/[^\\s0-9a-zA-Z\\-_]/.test(s);\n    };\n    //\n    var _noEnum = function (v) {\n        return {\n            value: v, enumerable: false, writable: true, configurable: true\n        };\n    };\n    /**\n     * extend String.prototype with relevant methods\n     */\n    var extendString = function () {\n        var _add = function (name, body) { return Object.defineProperty(String.prototype, name, _noEnum(body)); };\n        _add('fromBase64', function () { return decode(this); });\n        _add('toBase64', function (urlsafe) { return encode(this, urlsafe); });\n        _add('toBase64URI', function () { return encode(this, true); });\n        _add('toBase64URL', function () { return encode(this, true); });\n        _add('toUint8Array', function () { return toUint8Array(this); });\n    };\n    /**\n     * extend Uint8Array.prototype with relevant methods\n     */\n    var extendUint8Array = function () {\n        var _add = function (name, body) { return Object.defineProperty(Uint8Array.prototype, name, _noEnum(body)); };\n        _add('toBase64', function (urlsafe) { return fromUint8Array(this, urlsafe); });\n        _add('toBase64URI', function () { return fromUint8Array(this, true); });\n        _add('toBase64URL', function () { return fromUint8Array(this, true); });\n    };\n    /**\n     * extend Builtin prototypes with relevant methods\n     */\n    var extendBuiltins = function () {\n        extendString();\n        extendUint8Array();\n    };\n    var gBase64 = {\n        version: version,\n        VERSION: VERSION,\n        atob: _atob,\n        atobPolyfill: atobPolyfill,\n        btoa: _btoa,\n        btoaPolyfill: btoaPolyfill,\n        fromBase64: decode,\n        toBase64: encode,\n        encode: encode,\n        encodeURI: encodeURI,\n        encodeURL: encodeURI,\n        utob: utob,\n        btou: btou,\n        decode: decode,\n        isValid: isValid,\n        fromUint8Array: fromUint8Array,\n        toUint8Array: toUint8Array,\n        extendString: extendString,\n        extendUint8Array: extendUint8Array,\n        extendBuiltins: extendBuiltins\n    };\n    //\n    // export Base64 to the namespace\n    //\n    // ES5 is yet to have Object.assign() that may make transpilers unhappy.\n    // gBase64.Base64 = Object.assign({}, gBase64);\n    gBase64.Base64 = {};\n    Object.keys(gBase64).forEach(function (k) { return gBase64.Base64[k] = gBase64[k]; });\n    return gBase64;\n}));\n", "// The module cache\nvar __webpack_module_cache__ = {};\n\n// The require function\nfunction __webpack_require__(moduleId) {\n\t// Check if module is in cache\n\tvar cachedModule = __webpack_module_cache__[moduleId];\n\tif (cachedModule !== undefined) {\n\t\treturn cachedModule.exports;\n\t}\n\t// Create a new module (and put it into the cache)\n\tvar module = __webpack_module_cache__[moduleId] = {\n\t\t// no module.id needed\n\t\t// no module.loaded needed\n\t\texports: {}\n\t};\n\n\t// Execute the module function\n\t__webpack_modules__[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n\n\t// Return the exports of the module\n\treturn module.exports;\n}\n\n", "__webpack_require__.g = (function() {\n\tif (typeof globalThis === 'object') return globalThis;\n\ttry {\n\t\treturn this || new Function('return this')();\n\t} catch (e) {\n\t\tif (typeof window === 'object') return window;\n\t}\n})();", "/**\n * Copyright (c) 2023 The xterm.js authors. All rights reserved.\n * @license MIT\n */\n\nimport type { IDisposable, ITerminalAddon, Terminal } from '@xterm/xterm';\nimport { type IClipboardProvider, ClipboardSelectionType, type IBase64 } from '@xterm/addon-clipboard';\nimport { Base64 as JSBase64 } from 'js-base64';\n\nexport class ClipboardAddon implements ITerminalAddon {\n  private _terminal?: Terminal;\n  private _disposable?: IDisposable;\n\n  constructor(\n    private _base64: IBase64 = new Base64(),\n    private _provider: IClipboardProvider = new BrowserClipboardProvider()\n  ) {}\n\n  public activate(terminal: Terminal): void {\n    this._terminal = terminal;\n    this._disposable = terminal.parser.registerOscHandler(52, data => this._setOrReportClipboard(data));\n  }\n\n  public dispose(): void {\n    return this._disposable?.dispose();\n  }\n\n  private _readText(sel: ClipboardSelectionType, data: string): void {\n    const b64 = this._base64.encodeText(data);\n    this._terminal?.input(`\\x1b]52;${sel};${b64}\\x07`, false);\n  }\n\n  private _setOrReportClipboard(data: string): boolean | Promise<boolean> {\n    const args = data.split(';');\n    if (args.length < 2) {\n      return true;\n    }\n\n    const pc = args[0] as ClipboardSelectionType;\n    const pd = args[1];\n    if (pd === '?') {\n      const text = this._provider.readText(pc);\n\n      // Report clipboard\n      if (text instanceof Promise) {\n        return text.then((data) => {\n          this._readText(pc, data);\n          return true;\n        });\n      }\n\n      this._readText(pc, text);\n      return true;\n    }\n\n    // Clear clipboard if text is not a base64 encoded string.\n    let text = '';\n    try {\n      text = this._base64.decodeText(pd);\n    } catch {}\n\n\n    const result = this._provider.writeText(pc, text);\n    if (result instanceof Promise) {\n      return result.then(() => true);\n    }\n\n    return true;\n  }\n}\n\nexport class BrowserClipboardProvider implements IClipboardProvider {\n  public async readText(selection: ClipboardSelectionType): Promise<string> {\n    if (selection !== 'c') {\n      return Promise.resolve('');\n    }\n    return navigator.clipboard.readText();\n  }\n\n  public async writeText(selection: ClipboardSelectionType, text: string): Promise<void> {\n    if (selection !== 'c') {\n      return Promise.resolve();\n    }\n    return navigator.clipboard.writeText(text);\n  }\n}\n\nexport class Base64 implements IBase64 {\n  public encodeText(data: string): string {\n    return JSBase64.encode(data);\n  }\n  public decodeText(data: string): string {\n    const text = JSBase64.decode(data);\n    if (!JSBase64.isValid(data) || JSBase64.encode(text) !== data) {\n      return '';\n    }\n    return text;\n  }\n}\n"], "mappings": ";;;;;;;KAAA,SAA2CA,GAAMC,GAAAA;AAC1B,kBAAA,OAAZC,WAA0C,YAAA,OAAXC,SACxCA,OAAOD,UAAUD,EAAAA,IACQ,cAAA,OAAXG,UAAyBA,OAAOC,MAC9CD,OAAO,CAAA,GAAIH,CAAAA,IACe,YAAA,OAAZC,UACdA,QAAwB,iBAAID,EAAAA,IAE5BD,EAAqB,iBAAIC,EAAAA;IAC1B,EAAEK,MAAM,OAAA,MAAA;AAAA,UAAA,IAAA,EAAA,KAAA,SAAAC,IAAAC,IAAAC,IAAA;ACcU,uBAAA,OAATH,OAAuBA,OACT,eAAA,OAAXI,SAAyBA,SAAAA,WACrBD,GAAAE,KAAyBF,GAAAE,GAnBhCR,GAAOD,UAoBI,WAAA;AACjB;AAYA,cAWQU,IAXJC,KAAU,SAIVC,KAAUD,IACVE,KAA+B,cAAA,OAAXC,QACpBC,IAA6B,cAAA,OAAhBC,cAA6B,IAAIA,gBAAAA,QAC9CC,IAA6B,cAAA,OAAhBC,cAA6B,IAAIA,gBAAAA,QAE9CC,IAASC,MAAMC,UAAUC,MAAMC,KADvB,mEAAA,GAERC,KACId,KAAM,CAAC,GAGZS,EAFGM,QAAQ,SAAUC,IAAGC,IAAAA;AAAK,mBAAOjB,GAAIgB,EAAAA,IAAKC;UAAG,CAAA,GACxCjB,KAEPkB,IAAQ,2EACRC,IAAUC,OAAOC,aAAaC,KAAKF,MAAAA,GACnCG,IAAsC,cAAA,OAApBC,WAAWC,OAC3BD,WAAWC,KAAKH,KAAKE,UAAAA,IACrB,SAAUE,IAAAA;AAAM,mBAAO,IAAIF,WAAWd,MAAMC,UAAUC,MAAMC,KAAKa,IAAI,CAAA,CAAA;UAAK,GAC5EC,IAAa,SAAUC,IAAAA;AAAO,mBAAOA,GACpCC,QAAQ,MAAM,EAAA,EAAIA,QAAQ,UAAU,SAAUC,IAAAA;AAAM,qBAAa,OAANA,KAAY,MAAM;YAAK,CAAA;UAAI,GACvFC,IAAW,SAAUC,IAAAA;AAAK,mBAAOA,GAAEH,QAAQ,qBAAqB,EAAA;UAAK,GAIrEI,IAAe,SAAUC,IAAAA;AAIzB,qBAFIC,IAAKC,IAAIC,IAAIC,IAAIC,KAAM,IACvBC,KAAMN,GAAIO,SAAS,GACdxB,KAAI,GAAGA,KAAIiB,GAAIO,UAAS;AAC7B,mBAAKL,KAAKF,GAAIQ,WAAWzB,IAAAA,KAAQ,QAC5BoB,KAAKH,GAAIQ,WAAWzB,IAAAA,KAAQ,QAC5BqB,KAAKJ,GAAIQ,WAAWzB,IAAAA,KAAQ,IAC7B,OAAM,IAAI0B,UAAU,yBAAA;AAExBJ,cAAAA,MAAO9B,GADP0B,KAAOC,MAAM,KAAOC,MAAM,IAAKC,OACV,KAAK,EAAA,IACpB7B,EAAO0B,MAAO,KAAK,EAAA,IACnB1B,EAAO0B,MAAO,IAAI,EAAA,IAClB1B,EAAa,KAAN0B,EAAAA;YACjB;AACA,mBAAOK,KAAMD,GAAI3B,MAAM,GAAG4B,KAAM,CAAA,IAAK,MAAMI,UAAUJ,EAAAA,IAAOD;UAChE,GAMIM,IAAwB,cAAA,OAATC,OAAsB,SAAUZ,IAAAA;AAAO,mBAAOY,KAAKZ,EAAAA;UAAM,IACtE/B,KAAa,SAAU+B,IAAAA;AAAO,mBAAO9B,OAAOqB,KAAKS,IAAK,QAAA,EAAUa,SAAS,QAAA;UAAW,IAChFd,GACNe,IAAkB7C,KAChB,SAAU8C,IAAAA;AAAO,mBAAO7C,OAAOqB,KAAKwB,EAAAA,EAAKF,SAAS,QAAA;UAAW,IAC7D,SAAUE,IAAAA;AAIR,qBADIC,KAAO,CAAA,GACFjC,KAAI,GAAGkC,KAAIF,GAAIR,QAAQxB,KAAIkC,IAAGlC,MAFzB,KAGViC,CAAAA,GAAKE,KAAKjC,EAAQkC,MAAM,MAAMJ,GAAIK,SAASrC,IAAGA,KAHpC,IAAA,CAAA,CAAA;AAKd,mBAAO4B,EAAMK,GAAKK,KAAK,EAAA,CAAA;UAC3B,GAMAC,IAAiB,SAAUP,IAAKQ,IAAAA;AAEhC,mBAAA,WADIA,OAAsBA,KAAAA,QACnBA,KAAU9B,EAAWqB,EAAgBC,EAAAA,CAAAA,IAAQD,EAAgBC,EAAAA;UACxE,GAIIS,IAAU,SAAU1C,IAAAA;AACpB,gBAAIA,GAAEyB,SAAS,EAEX,SADIkB,KAAK3C,GAAE0B,WAAW,CAAA,KACV,MAAO1B,KACb2C,KAAK,OAASxC,EAAQ,MAAQwC,OAAO,CAAA,IACjCxC,EAAQ,MAAa,KAALwC,EAAAA,IACfxC,EAAQ,MAASwC,OAAO,KAAM,EAAA,IAC3BxC,EAAQ,MAASwC,OAAO,IAAK,EAAA,IAC7BxC,EAAQ,MAAa,KAALwC,EAAAA;AAG9B,gBAAIA,KAAK,QAC0B,QAA5B3C,GAAE0B,WAAW,CAAA,IAAK,UAClB1B,GAAE0B,WAAW,CAAA,IAAK;AACzB,mBAAQvB,EAAQ,MAASwC,OAAO,KAAM,CAAA,IAChCxC,EAAQ,MAASwC,OAAO,KAAM,EAAA,IAC9BxC,EAAQ,MAASwC,OAAO,IAAK,EAAA,IAC7BxC,EAAQ,MAAa,KAALwC,EAAAA;UAE9B,GACIC,IAAU,iDAMVC,IAAO,SAAUC,IAAAA;AAAK,mBAAOA,GAAEjC,QAAQ+B,GAASF,CAAAA;UAAU,GAE1DK,IAAU5D,KACR,SAAU6B,IAAAA;AAAK,mBAAO5B,OAAOqB,KAAKO,IAAG,MAAA,EAAQe,SAAS,QAAA;UAAW,IACjExC,IACI,SAAUyB,IAAAA;AAAK,mBAAOgB,EAAgBzC,EAAIyD,OAAOhC,EAAAA,CAAAA;UAAK,IACtD,SAAUA,IAAAA;AAAK,mBAAOa,EAAMgB,EAAK7B,EAAAA,CAAAA;UAAK,GAM5CgC,IAAS,SAAUpC,IAAK6B,IAAAA;AAExB,mBAAA,WADIA,OAAsBA,KAAAA,QACnBA,KACD9B,EAAWoC,EAAQnC,EAAAA,CAAAA,IACnBmC,EAAQnC,EAAAA;UAClB,GAKIqC,IAAY,SAAUrC,IAAAA;AAAO,mBAAOoC,EAAOpC,IAAAA,IAAK;UAAO,GAIvDsC,IAAU,+EACVC,IAAU,SAAUC,IAAAA;AACpB,oBAAQA,GAAK3B,QAAAA;cACT,KAAK;AACD,oBAGmC4B,OAHxB,IAAOD,GAAK1B,WAAW,CAAA,MAAO,MACjC,KAAO0B,GAAK1B,WAAW,CAAA,MAAO,MAC9B,KAAO0B,GAAK1B,WAAW,CAAA,MAAO,IAC/B,KAAO0B,GAAK1B,WAAW,CAAA,KAAmB;AACjD,uBAAQvB,EAA0B,SAAjBkD,OAAW,GAAA,IACtBlD,EAA2B,SAAT,OAATkD,GAAAA;cACnB,KAAK;AACD,uBAAOlD,GAAU,KAAOiD,GAAK1B,WAAW,CAAA,MAAO,MACvC,KAAO0B,GAAK1B,WAAW,CAAA,MAAO,IAC/B,KAAO0B,GAAK1B,WAAW,CAAA,CAAA;cAClC;AACI,uBAAOvB,GAAU,KAAOiD,GAAK1B,WAAW,CAAA,MAAO,IACxC,KAAO0B,GAAK1B,WAAW,CAAA,CAAA;YAAA;UAE1C,GAMI4B,IAAO,SAAUC,IAAAA;AAAK,mBAAOA,GAAE1C,QAAQqC,GAASC,CAAAA;UAAU,GAI1DK,IAAe,SAAUjC,IAAAA;AAGzB,gBADAA,KAAMA,GAAIV,QAAQ,QAAQ,EAAA,GAAA,CACrBX,EAAMuD,KAAKlC,EAAAA,EACZ,OAAM,IAAII,UAAU,mBAAA;AACxBJ,YAAAA,MAAO,KAAK3B,MAAM,KAAkB,IAAb2B,GAAIE,OAAAA;AAE3B,qBADIiC,IAAeC,IAAIC,IAAd1C,KAAM,IACNjB,KAAI,GAAGA,KAAIsB,GAAIE,SACpBiC,CAAAA,KAAM5D,EAAOyB,GAAIsC,OAAO5D,IAAAA,CAAAA,KAAS,KAC3BH,EAAOyB,GAAIsC,OAAO5D,IAAAA,CAAAA,KAAS,MAC1B0D,KAAK7D,EAAOyB,GAAIsC,OAAO5D,IAAAA,CAAAA,MAAU,KACjC2D,KAAK9D,EAAOyB,GAAIsC,OAAO5D,IAAAA,CAAAA,IAC9BiB,MAAc,OAAPyC,KAAYxD,EAAQuD,MAAO,KAAK,GAAA,IAC1B,OAAPE,KAAYzD,EAAQuD,MAAO,KAAK,KAAKA,MAAO,IAAI,GAAA,IAC5CvD,EAAQuD,MAAO,KAAK,KAAKA,MAAO,IAAI,KAAW,MAANA,EAAAA;AAEvD,mBAAOxC;UACX,GAMI4C,IAAwB,cAAA,OAATC,OAAsB,SAAUxC,IAAAA;AAAO,mBAAOwC,KAAKhD,EAASQ,EAAAA,CAAAA;UAAO,IAChFpC,KAAa,SAAUoC,IAAAA;AAAO,mBAAOnC,OAAOqB,KAAKc,IAAK,QAAA,EAAUQ,SAAS,QAAA;UAAW,IAChFyB,GAENQ,IAAgB7E,KACd,SAAU8E,IAAAA;AAAK,mBAAO1D,EAASnB,OAAOqB,KAAKwD,IAAG,QAAA,CAAA;UAAY,IAC1D,SAAUA,IAAAA;AAAK,mBAAO1D,EAASuD,EAAMG,EAAAA,EAAGC,MAAM,EAAA,EAAIC,IAAI,SAAUnE,IAAAA;AAAK,qBAAOA,GAAE0B,WAAW,CAAA;YAAI,CAAA,CAAA;UAAK,GAIpG0C,IAAe,SAAUH,IAAAA;AAAK,mBAAOD,EAAcK,EAAOJ,EAAAA,CAAAA;UAAK,GAE/DK,IAAUnF,KACR,SAAU8E,IAAAA;AAAK,mBAAO7E,OAAOqB,KAAKwD,IAAG,QAAA,EAAUlC,SAAS,MAAA;UAAS,IACjE1C,IACI,SAAU4E,IAAAA;AAAK,mBAAO5E,EAAIkF,OAAOP,EAAcC,EAAAA,CAAAA;UAAK,IACpD,SAAUA,IAAAA;AAAK,mBAAOX,EAAKQ,EAAMG,EAAAA,CAAAA;UAAK,GAC5CI,IAAS,SAAUJ,IAAAA;AAAK,mBAAOlD,EAASkD,GAAEpD,QAAQ,SAAS,SAAUC,IAAAA;AAAM,qBAAa,OAANA,KAAY,MAAM;YAAK,CAAA,CAAA;UAAK,GAM9GyD,IAAS,SAAU3D,IAAAA;AAAO,mBAAO0D,EAAQD,EAAOzD,EAAAA,CAAAA;UAAO,GAYvD4D,IAAU,SAAUC,IAAAA;AACpB,mBAAO,EACHC,OAAOD,IAAGE,YAAAA,OAAmBC,UAAAA,MAAgBC,cAAAA,KAAc;UAEnE,GAIIC,IAAe,WAAA;AACf,gBAAIC,KAAO,SAAUC,IAAMC,IAAAA;AAAQ,qBAAOC,OAAOC,eAAe/E,OAAOT,WAAWqF,IAAMR,EAAQS,EAAAA,CAAAA;YAAQ;AACxGF,YAAAA,GAAK,cAAc,WAAA;AAAc,qBAAOR,EAAOa,IAAAA;YAAO,CAAA,GACtDL,GAAK,YAAY,SAAUtC,IAAAA;AAAW,qBAAOO,EAAOoC,MAAM3C,EAAAA;YAAU,CAAA,GACpEsC,GAAK,eAAe,WAAA;AAAc,qBAAO/B,EAAOoC,MAAAA,IAAM;YAAO,CAAA,GAC7DL,GAAK,eAAe,WAAA;AAAc,qBAAO/B,EAAOoC,MAAAA,IAAM;YAAO,CAAA,GAC7DL,GAAK,gBAAgB,WAAA;AAAc,qBAAOX,EAAagB,IAAAA;YAAO,CAAA;UAClE,GAIIC,IAAmB,WAAA;AACnB,gBAAIN,KAAO,SAAUC,IAAMC,IAAAA;AAAQ,qBAAOC,OAAOC,eAAe3E,WAAWb,WAAWqF,IAAMR,EAAQS,EAAAA,CAAAA;YAAQ;AAC5GF,YAAAA,GAAK,YAAY,SAAUtC,IAAAA;AAAW,qBAAOD,EAAe4C,MAAM3C,EAAAA;YAAU,CAAA,GAC5EsC,GAAK,eAAe,WAAA;AAAc,qBAAOvC,EAAe4C,MAAAA,IAAM;YAAO,CAAA,GACrEL,GAAK,eAAe,WAAA;AAAc,qBAAOvC,EAAe4C,MAAAA,IAAM;YAAO,CAAA;UACzE,GAQIE,IAAU,EACVrG,SAASA,IACTC,SAASA,IACT6E,MAAMD,GACNN,cAAcA,GACd1B,MAAMD,GACNZ,cAAcA,GACdsE,YAAYhB,GACZiB,UAAUxC,GACVA,QAAQA,GACRC,WAAWA,GACXwC,WAAWxC,GACXJ,MAAMA,GACNS,MAAMA,GACNiB,QAAQA,GACRmB,SAtDU,SAAU9E,IAAAA;AACpB,gBAAmB,YAAA,OAARA,GACP,QAAA;AACJ,gBAAII,KAAIJ,GAAIC,QAAQ,QAAQ,EAAA,EAAIA,QAAQ,WAAW,EAAA;AACnD,mBAAA,CAAQ,oBAAoB4C,KAAKzC,EAAAA,KAAAA,CAAO,oBAAoByC,KAAKzC,EAAAA;UACrE,GAkDIwB,gBAAgBA,GAChB4B,cAAcA,GACdU,cAAcA,GACdO,kBAAkBA,GAClBM,gBAxBiB,WAAA;AACjBb,cAAAA,GACAO,EAAAA;UACJ,GA4BAC,QAAiB,CAAC,EAAA;AAElB,iBADAJ,OAAOU,KAAKN,CAAAA,EAASvF,QAAQ,SAAU8F,IAAAA;AAAK,mBAAOP,EAAQQ,OAAOD,EAAAA,IAAKP,EAAQO,EAAAA;UAAI,CAAA,GAC5EP;QACX,EAnT2BjH;MAAAA,EAAAA,GCLvB0H,IAA2B,CAAC;AAGhC,eAASC,EAAoBC,IAAAA;AAE5B,YAAIC,IAAeH,EAAyBE,EAAAA;AAC5C,YAAA,WAAIC,EACH,QAAOA,EAAa5H;AAGrB,YAAIC,IAASwH,EAAyBE,EAAAA,IAAY,EAGjD3H,SAAS,CAAC,EAAA;AAOX,eAHA6H,EAAoBF,EAAAA,EAAUpG,KAAKtB,EAAOD,SAASC,GAAQA,EAAOD,SAAS0H,CAAAA,GAGpEzH,EAAOD;MACf;ACtBA0H,QAAoBjH,IAAI,WAAA;AACvB,YAA0B,YAAA,OAAfqH,WAAyB,QAAOA;AAC3C,YAAA;AACC,iBAAOhB,QAAQ,IAAIiB,SAAS,aAAA,EAAb;QAChB,SAASzH,IAAAA;AACR,cAAsB,YAAA,OAAXE,OAAqB,QAAOA;QACxC;MACA,EAPuB;AAAA,UAAA,IAAA,CAAA;AAAA,cAAA,MAAA;AAAA;AAAA,YAAAH,KAAA;AAAA,eAAA,eAAAA,IAAA,cAAA,EAAA,OAAA,KAAA,CAAA,GAAAA,GAAA,SAAAA,GAAA,2BAAAA,GAAA,iBAAA;ACOxB,cAAAC,KAAA,EAAA,GAAA;AAEA,QAAAD,GAAA,iBAAA,MAAA;UAIE,YACU2H,KAAmB,IAAIR,KACvBS,KAAgC,IAAIC,KAAAA;AADpC,iBAAAF,UAAAA,IACA,KAAAC,YAAAA;UACP;UAEI,SAASE,IAAAA;AACdrB,iBAAKsB,YAAYD,IACjBrB,KAAKuB,cAAcF,GAASG,OAAOC,mBAAmB,IAAIC,CAAAA,OAAQ1B,KAAK2B,sBAAsBD,EAAAA,CAAAA;UAC/F;UAEO,UAAAE;AACL,mBAAO5B,KAAKuB,aAAaK,QAAAA;UAC3B;UAEQ,UAAUC,IAA6BH,IAAAA;AAC7C,kBAAMI,KAAM9B,KAAKkB,QAAQa,WAAWL,EAAAA;AACpC1B,iBAAKsB,WAAWU,MAAM,WAAWH,EAAAA,IAAOC,EAAAA,QAAAA,KAAW;UACrD;UAEQ,sBAAsBJ,IAAAA;AAC5B,kBAAMO,KAAOP,GAAK5C,MAAM,GAAA;AACxB,gBAAImD,GAAK5F,SAAS,EAChB,QAAA;AAGF,kBAAM6F,KAAKD,GAAK,CAAA,GACVE,KAAKF,GAAK,CAAA;AAChB,gBAAW,QAAPE,IAAY;AACd,oBAAMC,KAAOpC,KAAKmB,UAAUkB,SAASH,EAAAA;AAGrC,qBAAIE,cAAgBE,UACXF,GAAKG,KAAMb,CAAAA,QAChB1B,KAAKwC,UAAUN,IAAIR,EAAAA,GAAAA,KACZ,KAIX1B,KAAKwC,UAAUN,IAAIE,EAAAA,GAAAA;YACZ;AAIT,gBAAIA,KAAO;AACX,gBAAA;AACEA,cAAAA,KAAOpC,KAAKkB,QAAQuB,WAAWN,EAAAA;YAAAA,QAC/B;YAAM;AAGR,kBAAMO,KAAS1C,KAAKmB,UAAUwB,UAAUT,IAAIE,EAAAA;AAC5C,mBAAA,EAAIM,cAAkBJ,YACbI,GAAOH,KAAK,MAAA,IAAM;UAI7B;QAAA;QAGF,MAAanB,EAAAA;UACJ,MAAA,SAAewB,IAAAA;AACpB,mBAAkB,QAAdA,KACKN,QAAQO,QAAQ,EAAA,IAElBC,UAAUC,UAAUV,SAAAA;UAC7B;UAEO,MAAA,UAAgBO,IAAmCR,IAAAA;AACxD,mBAAkB,QAAdQ,KACKN,QAAQO,QAAAA,IAEVC,UAAUC,UAAUJ,UAAUP,EAAAA;UACvC;QAAA;AAbF,QAAA7I,GAAA,2BAAA;QAgBA,MAAamH,EAAAA;UACJ,WAAWgB,IAAAA;AAChB,mBAAOlI,GAAA,OAASoE,OAAO8D,EAAAA;UACzB;UACO,WAAWA,IAAAA;AAChB,kBAAMU,KAAO5I,GAAA,OAAS2F,OAAOuC,EAAAA;AAC7B,mBAAKlI,GAAA,OAAS8G,QAAQoB,EAAAA,KAASlI,GAAA,OAASoE,OAAOwE,EAAAA,MAAUV,KAGlDU,KAFE;UAGX;QAAA;AAVF,QAAA7I,GAAA,SAAA;MAAA,GAAA,GAAA;IAAA,GAAA,CAAA;;;", "names": ["root", "factory", "exports", "module", "define", "amd", "self", "t", "e", "r", "window", "g", "tab", "version", "VERSION", "_hasBuffer", "<PERSON><PERSON><PERSON>", "_TD", "TextDecoder", "_TE", "TextEncoder", "b64chs", "Array", "prototype", "slice", "call", "b64tab", "for<PERSON>ach", "c", "i", "b64re", "_fromCC", "String", "fromCharCode", "bind", "_U8Afrom", "Uint8Array", "from", "it", "_mkUriSafe", "src", "replace", "m0", "_tidyB64", "s", "btoaPolyfill", "bin", "u32", "c0", "c1", "c2", "asc", "pad", "length", "charCodeAt", "TypeError", "substring", "_btoa", "btoa", "toString", "_fromUint8Array", "u8a", "strs", "l", "push", "apply", "subarray", "join", "fromUint8Array", "urlsafe", "cb_utob", "cc", "re_utob", "utob", "u", "_encode", "encode", "encodeURI", "re_btou", "cb_btou", "cccc", "offset", "btou", "b", "atobPolyfill", "test", "u24", "r1", "r2", "char<PERSON>t", "_atob", "atob", "_toUint8Array", "a", "split", "map", "toUint8Array", "_unURI", "_decode", "decode", "_noEnum", "v", "value", "enumerable", "writable", "configurable", "extendString", "_add", "name", "body", "Object", "defineProperty", "this", "extendUint8Array", "gBase64", "fromBase64", "toBase64", "encodeURL", "<PERSON><PERSON><PERSON><PERSON>", "extendBuiltins", "keys", "k", "Base64", "__webpack_module_cache__", "__webpack_require__", "moduleId", "cachedModule", "__webpack_modules__", "globalThis", "Function", "_base64", "_provider", "BrowserClipboardProvider", "terminal", "_terminal", "_disposable", "parser", "registerOscHandler", "data", "_setOrReportClipboard", "dispose", "sel", "b64", "encodeText", "input", "args", "pc", "pd", "text", "readText", "Promise", "then", "_readText", "decodeText", "result", "writeText", "selection", "resolve", "navigator", "clipboard"]}