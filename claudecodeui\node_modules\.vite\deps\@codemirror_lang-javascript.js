import {
  autoCloseTags,
  completionPath,
  esLint,
  javascript,
  javascriptLanguage,
  jsxLanguage,
  localCompletionSource,
  scopeCompletionSource,
  snippets,
  tsxLanguage,
  typescriptLanguage,
  typescriptSnippets
} from "./chunk-KCNP2HHC.js";
import "./chunk-CZNPTDLH.js";
import "./chunk-O2JWGMKQ.js";
import "./chunk-PACPR5NY.js";
import "./chunk-2FMXLZOA.js";
import "./chunk-JEVQZFNC.js";
import "./chunk-G3PMV62Z.js";
export {
  autoCloseTags,
  completionPath,
  esLint,
  javascript,
  javascriptLanguage,
  jsxLanguage,
  localCompletionSource,
  scopeCompletionSource,
  snippets,
  tsxLanguage,
  typescriptLanguage,
  typescriptSnippets
};
